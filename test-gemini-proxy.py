#!/usr/bin/env python3
"""
测试Gemini代理是否工作
"""

import requests
import json
import time

def test_gemini_proxy():
    """测试Gemini代理服务"""
    print("🌐 测试 Gemini 代理服务...")
    
    proxy_tokens = [
        "sk-gemiban-token-1",
        "sk-gemiban-token-2", 
        "sk-gemiban-admin"
    ]
    
    proxy_url = "https://gemiban.pages.dev/api/v1"
    
    valid_tokens = 0
    
    for i, token in enumerate(proxy_tokens):
        print(f"\n🔍 测试代理Token {i+1}: {token}")
        
        try:
            # 测试模型列表
            print("   📋 测试模型列表...")
            response = requests.get(
                f"{proxy_url}/models",
                headers={
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                models_data = response.json()
                if 'data' in models_data:
                    models = [model['id'] for model in models_data['data']]
                    print(f"   ✅ 模型列表获取成功: {', '.join(models)}")
                else:
                    print(f"   ❌ 模型列表格式异常: {models_data}")
                    continue
            else:
                print(f"   ❌ 模型列表获取失败 - 状态码: {response.status_code}")
                print(f"   错误: {response.text[:100]}")
                continue
            
            # 测试聊天完成
            print("   💬 测试聊天完成...")
            response = requests.post(
                f"{proxy_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gemini-1.5-flash",
                    "messages": [{"role": "user", "content": "Hello, test proxy"}],
                    "max_tokens": 50
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    content = data['choices'][0]['message']['content']
                    print(f"   ✅ 聊天测试成功")
                    print(f"   响应: {content[:50]}...")
                    valid_tokens += 1
                else:
                    print(f"   ❌ 聊天响应格式异常: {data}")
            else:
                print(f"   ❌ 聊天测试失败 - 状态码: {response.status_code}")
                print(f"   错误: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ 代理测试失败: {e}")
        
        time.sleep(2)
    
    print(f"\n📊 代理测试结果: {valid_tokens}/{len(proxy_tokens)} 个Token有效")
    return valid_tokens

def test_direct_gemini():
    """测试直接访问Gemini API（用于对比）"""
    print("\n🔗 测试直接访问 Gemini API...")
    
    gemini_keys = [
        "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ",
        "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
    ]
    
    valid_keys = 0
    
    for i, key in enumerate(gemini_keys):
        print(f"\n🔍 测试直接访问 Key {i+1}: {key[:20]}...")
        
        try:
            response = requests.post(
                f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={key}",
                headers={"Content-Type": "application/json"},
                json={
                    "contents": [{
                        "parts": [{"text": "Hello, direct test"}]
                    }]
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'candidates' in data and len(data['candidates']) > 0:
                    content = data['candidates'][0]['content']['parts'][0]['text']
                    print(f"   ✅ 直接访问成功")
                    print(f"   响应: {content[:50]}...")
                    valid_keys += 1
                else:
                    print(f"   ❌ 直接访问响应格式异常")
            else:
                print(f"   ❌ 直接访问失败 - 状态码: {response.status_code}")
                print(f"   错误: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ 直接访问失败: {e}")
        
        time.sleep(2)
    
    print(f"\n📊 直接访问结果: {valid_keys}/{len(gemini_keys)} 个Key有效")
    return valid_keys

def test_proxy_status():
    """测试代理服务状态"""
    print("\n📊 测试代理服务状态...")
    
    try:
        response = requests.get(
            "https://gemiban.pages.dev/api/status",
            headers={
                "Authorization": "Bearer sk-gemiban-admin"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            status = response.json()
            print("✅ 代理服务状态正常")
            print(f"   服务状态: {status.get('status', 'unknown')}")
            print(f"   API Keys数量: {status.get('api_keys_count', 'unknown')}")
            print(f"   预估每日请求: {status.get('estimated_daily_requests', 'unknown')}")
            return True
        else:
            print(f"❌ 代理服务状态异常 - 状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理服务状态检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Gemini 代理测试工具")
    print("=" * 50)
    
    # 测试代理服务状态
    proxy_status = test_proxy_status()
    
    # 测试代理访问
    proxy_valid = test_gemini_proxy()
    
    # 测试直接访问（用于对比）
    direct_valid = test_direct_gemini()
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    print(f"🌐 代理服务状态: {'✅ 正常' if proxy_status else '❌ 异常'}")
    print(f"🔗 代理访问: {proxy_valid}/3 个Token有效")
    print(f"🌍 直接访问: {direct_valid}/2 个Key有效")
    
    if proxy_valid >= 1:
        print("\n🎉 Gemini代理配置成功！")
        print("\n✅ 优势:")
        print("   - 国内可直接访问，无需翻墙")
        print("   - 自动负载均衡")
        print("   - OpenAI兼容格式")
        print("   - 全球CDN加速")
        
        print("\n🔧 AIWriteX配置:")
        print("   - API类型: Gemini 或 GeminiProxy")
        print("   - API Key: sk-gemiban-token-1")
        print("   - Base URL: https://gemiban.pages.dev/api/v1")
        print("   - 模型: gemini-1.5-flash")
        
    else:
        print("\n⚠️  代理配置需要检查:")
        print("   - 检查Cloudflare Pages部署状态")
        print("   - 确认代理服务正常运行")
        print("   - 验证API Keys配置")
    
    if direct_valid >= 1 and proxy_valid == 0:
        print("\n💡 建议:")
        print("   - 代理暂时不可用，可以先使用直接访问")
        print("   - 需要配置代理或VPN访问Gemini API")

if __name__ == "__main__":
    main()
