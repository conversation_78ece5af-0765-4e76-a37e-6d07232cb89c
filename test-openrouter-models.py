#!/usr/bin/env python3
"""
测试OpenRouter不同免费模型的可用性
"""

import requests
import json
import time

def test_openrouter_models():
    """测试OpenRouter不同免费模型"""
    print("🔑 测试 OpenRouter 不同免费模型...")
    
    # 您的所有OpenRouter Keys
    openrouter_keys = [
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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"
    ]
    
    # 常见的免费模型
    free_models = [
        "meta-llama/llama-3.2-3b-instruct:free",
        "microsoft/phi-3-mini-128k-instruct:free", 
        "qwen/qwen-2-7b-instruct:free",
        "google/gemma-2-9b-it:free",
        "huggingfaceh4/zephyr-7b-beta:free",
        "openchat/openchat-7b:free",
        "gryphe/mythomist-7b:free",
        "undi95/toppy-m-7b:free",
        "openrouter/auto"  # 自动选择免费模型
    ]
    
    results = {}
    
    # 测试每个模型
    for model in free_models:
        print(f"\n🤖 测试模型: {model}")
        results[model] = {"valid_keys": 0, "working_keys": []}
        
        # 只测试前5个Keys来节省时间
        for i, key in enumerate(openrouter_keys[:5]):
            try:
                response = requests.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "https://aiwritex.com",
                        "X-Title": "AIWriteX Model Test"
                    },
                    json={
                        "model": model,
                        "messages": [{"role": "user", "content": "Hello"}],
                        "max_tokens": 20
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'choices' in data and len(data['choices']) > 0:
                        results[model]["valid_keys"] += 1
                        results[model]["working_keys"].append(f"Key{i+1}")
                        print(f"   ✅ Key {i+1} 可用")
                    else:
                        print(f"   ❌ Key {i+1} 响应格式异常")
                elif response.status_code == 402:
                    print(f"   💰 Key {i+1} 余额不足")
                elif response.status_code == 429:
                    print(f"   ⏰ Key {i+1} 请求过频")
                else:
                    print(f"   ❌ Key {i+1} 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ Key {i+1} 错误: {str(e)[:50]}")
            
            time.sleep(1)  # 避免请求过快
    
    return results

def get_available_models(api_key):
    """获取可用模型列表"""
    print(f"\n📋 获取可用模型列表...")
    
    try:
        response = requests.get(
            "https://openrouter.ai/api/v1/models",
            headers={
                "Authorization": f"Bearer {api_key}",
                "HTTP-Referer": "https://aiwritex.com",
                "X-Title": "AIWriteX"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data:
                free_models = []
                for model in data['data']:
                    # 检查是否为免费模型
                    if (model.get('pricing', {}).get('prompt', '0') == '0' or 
                        ':free' in model['id'] or
                        model.get('pricing', {}).get('prompt', 0) == 0):
                        free_models.append(model['id'])
                
                print(f"✅ 找到 {len(free_models)} 个免费模型")
                return free_models[:10]  # 返回前10个
            else:
                print("❌ 模型列表格式异常")
                return []
        else:
            print(f"❌ 获取模型列表失败 - 状态码: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 获取模型列表错误: {e}")
        return []

def main():
    """主函数"""
    print("🚀 OpenRouter 免费模型测试工具")
    print("=" * 60)
    
    # 使用第一个Key获取可用模型
    first_key = "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"
    available_models = get_available_models(first_key)
    
    if available_models:
        print(f"\n🎯 发现的免费模型:")
        for i, model in enumerate(available_models, 1):
            print(f"   {i}. {model}")
    
    # 测试不同模型
    results = test_openrouter_models()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    best_model = None
    max_valid_keys = 0
    
    for model, result in results.items():
        valid_count = result["valid_keys"]
        print(f"\n🤖 {model}")
        print(f"   ✅ 可用Keys: {valid_count}/5")
        print(f"   🔑 工作的Keys: {', '.join(result['working_keys'])}")
        
        if valid_count > max_valid_keys:
            max_valid_keys = valid_count
            best_model = model
    
    print(f"\n🏆 推荐配置:")
    if best_model:
        print(f"   最佳模型: {best_model}")
        print(f"   可用Keys: {max_valid_keys}/5")
        
        print(f"\n🔧 AIWriteX 配置建议:")
        print(f"   api_type: OpenRouter")
        print(f"   model: {best_model}")
        print(f"   预估可用Keys: {max_valid_keys * 4} (基于19个Keys)")
        
        # 更新配置建议
        if max_valid_keys >= 3:
            print(f"\n✅ 配置状态: 良好")
            print(f"   - 足够的可用Keys支持正常使用")
            print(f"   - 建议使用模型: {best_model}")
        else:
            print(f"\n⚠️  配置状态: 需要优化")
            print(f"   - 可用Keys较少，可能影响使用体验")
            print(f"   - 建议尝试其他免费模型")
    else:
        print("❌ 未找到可用的模型配置")
        print("   建议检查API Keys状态或联系OpenRouter支持")

if __name__ == "__main__":
    main()
