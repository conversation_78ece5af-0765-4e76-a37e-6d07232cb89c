// Cloudflare Pages Functions - API代理
// 这个文件会自动处理 /api/* 路径的请求

// 配置您的API Keys
const API_KEYS = [
  "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ",
  "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
];

// 允许的访问令牌
const ALLOWED_TOKENS = [
  "sk-gemiban-token-1",
  "sk-gemiban-token-2", 
  "sk-gemiban-admin"
];

// 管理员令牌
const AUTH_TOKEN = "sk-gemiban-admin";

// API Key使用统计
let keyUsageStats = {};

// 获取下一个可用的API Key
function getNextApiKey() {
  // 简单的轮询策略
  const now = Date.now();
  let bestKey = API_KEYS[0];
  let minUsage = Infinity;
  
  for (const key of API_KEYS) {
    const usage = keyUsageStats[key] || { count: 0, lastUsed: 0 };
    
    // 如果超过1分钟没使用，重置计数
    if (now - usage.lastUsed > 60000) {
      usage.count = 0;
    }
    
    if (usage.count < minUsage) {
      minUsage = usage.count;
      bestKey = key;
    }
  }
  
  // 更新使用统计
  if (!keyUsageStats[bestKey]) {
    keyUsageStats[bestKey] = { count: 0, lastUsed: 0 };
  }
  keyUsageStats[bestKey].count++;
  keyUsageStats[bestKey].lastUsed = now;
  
  return bestKey;
}

// 验证访问令牌
function validateToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return ALLOWED_TOKENS.includes(token);
}

// 处理CORS预检请求
function handleCORS(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
      },
    });
  }
  return null;
}

// 转换OpenAI格式到Gemini格式
function convertOpenAIToGemini(openaiRequest) {
  const { model, messages, stream = false, max_tokens, temperature } = openaiRequest;
  
  // 转换消息格式
  const contents = [];
  let currentContent = { parts: [] };
  
  for (const message of messages) {
    if (message.role === 'system') {
      // 系统消息作为第一个用户消息的前缀
      currentContent.parts.push({ text: `System: ${message.content}\n\n` });
    } else if (message.role === 'user') {
      if (currentContent.parts.length > 0) {
        contents.push(currentContent);
        currentContent = { parts: [] };
      }
      currentContent.parts.push({ text: message.content });
    } else if (message.role === 'assistant') {
      if (currentContent.parts.length > 0) {
        contents.push(currentContent);
      }
      contents.push({
        role: 'model',
        parts: [{ text: message.content }]
      });
      currentContent = { parts: [] };
    }
  }
  
  if (currentContent.parts.length > 0) {
    contents.push(currentContent);
  }
  
  const geminiRequest = {
    contents,
    generationConfig: {}
  };
  
  if (max_tokens) {
    geminiRequest.generationConfig.maxOutputTokens = max_tokens;
  }
  
  if (temperature !== undefined) {
    geminiRequest.generationConfig.temperature = temperature;
  }
  
  return { geminiRequest, model: model || 'gemini-1.5-flash', stream };
}

// 转换Gemini响应到OpenAI格式
function convertGeminiToOpenAI(geminiResponse, model) {
  if (!geminiResponse.candidates || geminiResponse.candidates.length === 0) {
    throw new Error('No candidates in Gemini response');
  }
  
  const candidate = geminiResponse.candidates[0];
  const content = candidate.content.parts[0].text;
  
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: [{
      index: 0,
      message: {
        role: 'assistant',
        content: content
      },
      finish_reason: 'stop'
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}

// 主要的请求处理函数
export async function onRequest(context) {
  const { request, env } = context;
  const url = new URL(request.url);
  const path = url.pathname;
  
  // 处理CORS
  const corsResponse = handleCORS(request);
  if (corsResponse) return corsResponse;
  
  // 验证访问令牌
  if (!validateToken(request)) {
    return new Response(JSON.stringify({ error: 'Invalid or missing authorization token' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
  
  try {
    // 路由处理
    if (path.includes('/v1/models')) {
      // 返回支持的模型列表
      const models = {
        object: 'list',
        data: [
          { id: 'gemini-1.5-flash', object: 'model', created: 1677610602, owned_by: 'google' },
          { id: 'gemini-1.5-pro', object: 'model', created: 1677610602, owned_by: 'google' },
          { id: 'gemini-2.0-flash-exp', object: 'model', created: 1677610602, owned_by: 'google' }
        ]
      };
      
      return new Response(JSON.stringify(models), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
    
    if (path.includes('/v1/chat/completions')) {
      // 处理聊天完成请求
      const requestBody = await request.json();
      const { geminiRequest, model, stream } = convertOpenAIToGemini(requestBody);
      
      const apiKey = getNextApiKey();
      const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:${stream ? 'streamGenerateContent' : 'generateContent'}?key=${apiKey}`;
      
      const geminiResponse = await fetch(geminiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(geminiRequest),
      });
      
      if (!geminiResponse.ok) {
        const errorText = await geminiResponse.text();
        return new Response(JSON.stringify({ error: errorText }), {
          status: geminiResponse.status,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        });
      }
      
      if (stream) {
        // 流式响应处理
        return new Response(geminiResponse.body, {
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
          },
        });
      } else {
        // 普通响应处理
        const geminiData = await geminiResponse.json();
        const openaiResponse = convertGeminiToOpenAI(geminiData, model);
        
        return new Response(JSON.stringify(openaiResponse), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        });
      }
    }
    
    if (path.includes('/status')) {
      // 返回服务状态
      const status = {
        status: 'running',
        api_keys_count: API_KEYS.length,
        estimated_daily_requests: API_KEYS.length * 1500,
        estimated_hourly_requests: API_KEYS.length * 62,
        key_usage_stats: keyUsageStats
      };
      
      return new Response(JSON.stringify(status), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
    
    // 默认返回404
    return new Response(JSON.stringify({ error: 'Not Found' }), {
      status: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
    
  } catch (error) {
    console.error('API Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
