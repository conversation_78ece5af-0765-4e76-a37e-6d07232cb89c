#!/bin/bash

echo "🌐 Cloudflare Tunnel 部署脚本"
echo "================================"

# 检查是否安装了cloudflared
if ! command -v cloudflared &> /dev/null; then
    echo "📥 安装 cloudflared..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install cloudflared
        else
            echo "请先安装 Homebrew 或手动下载 cloudflared"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
        sudo dpkg -i cloudflared-linux-amd64.deb
        rm cloudflared-linux-amd64.deb
    else
        echo "请手动安装 cloudflared: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/"
        exit 1
    fi
fi

echo "✅ cloudflared 已安装"

# 登录 Cloudflare
echo "🔐 登录 Cloudflare..."
echo "请在浏览器中完成登录..."
cloudflared tunnel login

# 创建隧道
TUNNEL_NAME="gemini-balance-$(date +%s)"
echo "🚇 创建隧道: $TUNNEL_NAME"
cloudflared tunnel create $TUNNEL_NAME

# 获取隧道ID
TUNNEL_ID=$(cloudflared tunnel list | grep $TUNNEL_NAME | awk '{print $1}')
echo "📝 隧道ID: $TUNNEL_ID"

# 配置DNS
read -p "请输入您的域名 (例如: api.yourdomain.com): " DOMAIN
echo "🌍 配置DNS记录..."
cloudflared tunnel route dns $TUNNEL_NAME $DOMAIN

# 创建配置文件
CONFIG_DIR="$HOME/.cloudflared"
mkdir -p $CONFIG_DIR

cat > $CONFIG_DIR/config.yml << EOF
tunnel: $TUNNEL_ID
credentials-file: $CONFIG_DIR/$TUNNEL_ID.json

ingress:
  - hostname: $DOMAIN
    service: http://localhost:8000
  - service: http_status:404
EOF

echo "📄 配置文件已创建: $CONFIG_DIR/config.yml"

# 启动本地服务
echo "🐳 启动 Gemini-Balance 服务..."
if [ -f "docker-compose-simple.yml" ]; then
    docker-compose -f docker-compose-simple.yml up -d
    echo "✅ Docker 服务已启动"
else
    echo "❌ 未找到 docker-compose-simple.yml 文件"
    echo "请先运行: ./quick-deploy.sh"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 本地服务运行正常"
else
    echo "❌ 本地服务启动失败，请检查日志"
    docker-compose -f docker-compose-simple.yml logs
    exit 1
fi

# 启动隧道
echo "🚇 启动 Cloudflare Tunnel..."
echo "隧道将在后台运行..."

# 创建启动脚本
cat > start-tunnel.sh << EOF
#!/bin/bash
echo "🚇 启动 Cloudflare Tunnel..."
cloudflared tunnel run $TUNNEL_NAME
EOF

chmod +x start-tunnel.sh

# 创建停止脚本
cat > stop-tunnel.sh << EOF
#!/bin/bash
echo "🛑 停止 Cloudflare Tunnel..."
pkill cloudflared
docker-compose -f docker-compose-simple.yml down
echo "✅ 服务已停止"
EOF

chmod +x stop-tunnel.sh

echo ""
echo "🎉 部署完成！"
echo "================================"
echo "🌐 访问地址: https://$DOMAIN"
echo "🔧 管理界面: https://$DOMAIN/keys_status"
echo "🔑 管理员令牌: sk-gemiban-admin"
echo ""
echo "📋 管理命令:"
echo "   启动隧道: ./start-tunnel.sh"
echo "   停止服务: ./stop-tunnel.sh"
echo "   查看日志: docker-compose -f docker-compose-simple.yml logs -f"
echo ""
echo "🚀 现在启动隧道..."
./start-tunnel.sh
