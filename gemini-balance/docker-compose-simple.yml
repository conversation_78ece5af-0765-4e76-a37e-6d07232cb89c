version: '3.8'

services:
  gemini-balance:
    image: ghcr.io/snailyp/gemini-balance:latest
    container_name: gemini-balance
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # 数据库配置 - 使用SQLite简化部署
      DATABASE_TYPE: sqlite
      SQLITE_DATABASE: /app/data/gemini_balance.db
      
      # API Keys配置 - 您的两个Gemini API Keys
      API_KEYS: '["AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ","AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"]'
      
      # 访问令牌配置
      ALLOWED_TOKENS: '["sk-gemiban-token-1","sk-gemiban-token-2","sk-gemiban-admin"]'
      AUTH_TOKEN: sk-gemiban-admin
      
      # 性能配置
      TEST_MODEL: gemini-1.5-flash
      MAX_FAILURES: 3
      MAX_RETRIES: 3
      CHECK_INTERVAL_HOURS: 1
      TIME_OUT: 300
      
      # 模型配置
      IMAGE_MODELS: '["gemini-2.0-flash-exp"]'
      SEARCH_MODELS: '["gemini-2.0-flash-exp"]'
      
      # 日志配置
      LOG_LEVEL: info
      AUTO_DELETE_ERROR_LOGS_ENABLED: true
      AUTO_DELETE_ERROR_LOGS_DAYS: 7
      
      # 其他配置
      TIMEZONE: Asia/Shanghai
      FAKE_STREAM_ENABLED: true
      
    volumes:
      - ./data:/app/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  data:
