# Gemini-Balance Cloudflare 部署指南

## 1. 准备工作

### 安装 Wrangler CLI
```bash
npm install -g wrangler
```

### 登录 Cloudflare
```bash
wrangler login
```

## 2. 配置环境变量

### 设置敏感信息（通过命令行）
```bash
# 设置API Keys
wrangler secret put API_KEYS
# 输入: ["AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ","AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"]

# 设置访问令牌
wrangler secret put ALLOWED_TOKENS
# 输入: ["sk-gemiban-token-1","sk-gemiban-token-2","sk-gemiban-admin"]

# 设置管理员令牌
wrangler secret put AUTH_TOKEN
# 输入: sk-gemiban-admin
```

## 3. 创建必要的资源

### 创建 KV 命名空间
```bash
# 创建生产环境 KV
wrangler kv:namespace create "GEMINI_BALANCE_KV"

# 创建预览环境 KV
wrangler kv:namespace create "GEMINI_BALANCE_KV" --preview
```

### 创建 R2 存储桶
```bash
wrangler r2 bucket create gemini-balance-data
```

## 4. 更新 wrangler.toml

将创建的资源ID更新到 `wrangler.toml` 文件中：

```toml
[[kv_namespaces]]
binding = "GEMINI_BALANCE_KV"
id = "你的KV命名空间ID"
preview_id = "你的预览KV命名空间ID"
```

## 5. 部署到 Cloudflare Workers

```bash
# 部署到生产环境
wrangler deploy

# 或者部署到特定环境
wrangler deploy --env production
```

## 6. 设置自定义域名

### 在 Cloudflare Dashboard 中：
1. 进入 Workers & Pages
2. 选择你的 Worker
3. 点击 "Custom domains"
4. 添加你的域名（如：api.yourdomain.com）

### 或使用命令行：
```bash
wrangler route add "api.yourdomain.com/*" gemini-balance
```

## 7. 测试部署

### 测试 API 端点
```bash
# 测试模型列表
curl -H "Authorization: Bearer sk-gemiban-token-1" \
     https://api.yourdomain.com/v1/models

# 测试聊天完成
curl -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-gemiban-token-1" \
     -d '{
       "model": "gemini-1.5-flash",
       "messages": [{"role": "user", "content": "Hello!"}]
     }' \
     https://api.yourdomain.com/v1/chat/completions
```

### 访问管理界面
```
https://api.yourdomain.com/keys_status
```
使用 `sk-gemiban-admin` 登录

## 8. 监控和日志

### 查看实时日志
```bash
wrangler tail
```

### 查看部署状态
```bash
wrangler status
```

## 9. 更新部署

当需要更新代码时：
```bash
git pull origin main
wrangler deploy
```

## 10. 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `API_KEYS` | Gemini API Keys数组 | `["key1","key2"]` |
| `ALLOWED_TOKENS` | 允许的访问令牌 | `["sk-token1"]` |
| `AUTH_TOKEN` | 管理员令牌 | `sk-admin` |
| `DATABASE_TYPE` | 数据库类型 | `sqlite` |
| `MAX_FAILURES` | 最大失败次数 | `3` |
| `MAX_RETRIES` | 最大重试次数 | `3` |

## 11. 故障排除

### 常见问题：
1. **部署失败**：检查 wrangler.toml 配置
2. **API 调用失败**：验证 API Keys 是否正确
3. **权限错误**：确认访问令牌配置正确

### 查看错误日志：
```bash
wrangler tail --format pretty
```

## 12. 成本估算

Cloudflare Workers 免费计划：
- 100,000 请求/天
- 10ms CPU 时间/请求
- 完全免费

付费计划（$5/月）：
- 10,000,000 请求/月
- 50ms CPU 时间/请求
- 更高的并发限制
