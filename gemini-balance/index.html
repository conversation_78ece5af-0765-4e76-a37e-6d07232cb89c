<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Balance - API代理服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .api-tester {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .response-area {
            background: #263238;
            color: #fff;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .status-item h3 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .status-item p {
            opacity: 0.9;
        }
        
        .endpoint-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .endpoint-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            font-family: 'Monaco', 'Menlo', monospace;
            word-break: break-all;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Gemini Balance</h1>
            <p>高性能Gemini API代理服务 - 支持负载均衡的免费API</p>
        </div>
        
        <div class="status-grid">
            <div class="status-item">
                <h3>2</h3>
                <p>API Keys</p>
            </div>
            <div class="status-item">
                <h3>3,000</h3>
                <p>每日请求</p>
            </div>
            <div class="status-item">
                <h3>125</h3>
                <p>每小时请求</p>
            </div>
            <div class="status-item">
                <h3>🟢</h3>
                <p>服务状态</p>
            </div>
        </div>
        
        <div class="card">
            <h2>📡 API服务</h2>
            <div class="endpoint-list">
                <div class="endpoint-item">
                    <strong>API端点:</strong> https://gemiban.pages.dev/api/v1
                </div>
                <div class="endpoint-item">
                    <strong>兼容格式:</strong> OpenAI API 标准格式
                </div>
                <div class="endpoint-item">
                    <strong>支持模型:</strong> Gemini 1.5 Flash, Gemini 1.5 Pro, Gemini 2.0 Flash
                </div>
            </div>

            <h3>🔑 访问说明</h3>
            <div class="endpoint-list">
                <div class="endpoint-item">🔐 需要有效的Bearer Token进行身份验证</div>
                <div class="endpoint-item">📧 如需获取访问权限，请联系管理员</div>
                <div class="endpoint-item">📖 完全兼容OpenAI API调用格式</div>
                <div class="endpoint-item">⚡ 支持流式响应和批量请求</div>
            </div>
        </div>
        
        <div class="card">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('chat')">💬 聊天测试</div>
                <div class="tab" onclick="switchTab('models')">📋 模型列表</div>
                <div class="tab" onclick="switchTab('direct')">🔗 直接调用</div>
            </div>
            
            <div id="chat-tab" class="tab-content active">
                <h3>💬 聊天测试</h3>
                <div class="api-tester">
                    <div class="form-group">
                        <label>API Token:</label>
                        <input type="text" id="chatToken" value="" placeholder="请输入您的API Token">
                    </div>
                    <div class="form-group">
                        <label>模型:</label>
                        <select id="chatModel">
                            <option value="gemini-1.5-flash">gemini-1.5-flash</option>
                            <option value="gemini-1.5-pro">gemini-1.5-pro</option>
                            <option value="gemini-2.0-flash-exp">gemini-2.0-flash-exp</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>消息内容:</label>
                        <textarea id="chatMessage" rows="3" placeholder="输入您的消息...">你好，请介绍一下自己</textarea>
                    </div>
                    <button class="btn" onclick="testChat()">发送消息</button>
                    <button class="btn btn-success" onclick="testStream()">流式对话</button>
                    
                    <div class="loading" id="chatLoading">
                        <div class="spinner"></div>
                        <p>正在处理请求...</p>
                    </div>
                    <div class="response-area" id="chatResponse" style="display:none;"></div>
                </div>
            </div>
            
            <div id="models-tab" class="tab-content">
                <h3>📋 模型列表</h3>
                <div class="api-tester">
                    <div class="form-group">
                        <label>API Token:</label>
                        <input type="text" id="modelsToken" value="" placeholder="请输入您的API Token">
                    </div>
                    <button class="btn" onclick="getModels()">获取模型列表</button>
                    
                    <div class="loading" id="modelsLoading">
                        <div class="spinner"></div>
                        <p>正在获取模型列表...</p>
                    </div>
                    <div class="response-area" id="modelsResponse" style="display:none;"></div>
                </div>
            </div>
            
            <div id="direct-tab" class="tab-content">
                <h3>🔗 直接调用Gemini API</h3>
                <div class="api-tester">
                    <div class="form-group">
                        <label>API Key:</label>
                        <input type="text" id="directKey" value="" placeholder="请输入Gemini API Key">
                    </div>
                    <div class="form-group">
                        <label>消息内容:</label>
                        <textarea id="directMessage" rows="3" placeholder="输入您的消息...">Hello, how are you?</textarea>
                    </div>
                    <button class="btn" onclick="testDirect()">直接调用</button>
                    
                    <div class="loading" id="directLoading">
                        <div class="spinner"></div>
                        <p>正在调用API...</p>
                    </div>
                    <div class="response-area" id="directResponse" style="display:none;"></div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>📚 使用说明</h2>
            <p><strong>1. 获取API Token:</strong> 使用上面提供的Token进行测试</p>
            <p><strong>2. 调用API:</strong> 使用OpenAI兼容的格式调用我们的API</p>
            <p><strong>3. 负载均衡:</strong> 系统会自动在多个API Key之间进行负载均衡</p>
            <p><strong>4. 错误处理:</strong> 自动重试和故障转移</p>
            
            <h3 style="margin-top: 20px;">📖 代码示例</h3>
            <div class="response-area">
# Python示例
import openai

client = openai.OpenAI(
    api_key="sk-gemiban-token-1",
    base_url="https://your-domain.pages.dev/api"
)

response = client.chat.completions.create(
    model="gemini-1.5-flash",
    messages=[{"role": "user", "content": "Hello!"}]
)

print(response.choices[0].message.content)
            </div>
        </div>
    </div>

    <script>
        // 安全提示
        function showSecurityWarning() {
            alert('⚠️ 安全提示：请不要在公共场所输入真实的API Keys！\n\n此页面仅用于演示API调用格式。\n如需正式使用，请联系管理员获取访问权限。');
        }

        // 验证Token格式
        function validateToken(token) {
            if (!token || token.trim() === '') {
                alert('❌ 请输入有效的API Token');
                return false;
            }
            if (!token.startsWith('sk-') && !token.startsWith('AIza')) {
                alert('❌ Token格式不正确\n\n正确格式：\n- API Token: sk-xxxxxx\n- Gemini Key: AIzaxxxxxx');
                return false;
            }
            return true;
        }
        
        // 切换标签
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
        
        // 显示加载状态
        function showLoading(loadingId, responseId) {
            document.getElementById(loadingId).style.display = 'block';
            document.getElementById(responseId).style.display = 'none';
        }
        
        // 隐藏加载状态
        function hideLoading(loadingId, responseId, content) {
            document.getElementById(loadingId).style.display = 'none';
            const responseEl = document.getElementById(responseId);
            responseEl.style.display = 'block';
            responseEl.textContent = content;
        }
        
        // 测试聊天
        async function testChat() {
            const token = document.getElementById('chatToken').value;
            const model = document.getElementById('chatModel').value;
            const message = document.getElementById('chatMessage').value;

            if (!validateToken(token) || !message) {
                return;
            }

            showLoading('chatLoading', 'chatResponse');

            try {
                // 使用代理API而不是直接调用
                const response = await fetch(`https://gemiban.pages.dev/api/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [{
                            role: 'user',
                            content: message
                        }]
                    })
                });
                
                const data = await response.json();

                if (response.ok && data.choices && data.choices.length > 0) {
                    const content = data.choices[0].message.content;
                    hideLoading('chatLoading', 'chatResponse', `✅ 响应成功:\n\n${content}`);
                } else {
                    hideLoading('chatLoading', 'chatResponse', `❌ 请求失败:\n\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                hideLoading('chatLoading', 'chatResponse', `❌ 网络错误:\n\n${error.message}`);
            }
        }
        
        // 测试流式对话
        async function testStream() {
            const token = document.getElementById('chatToken').value;
            const model = document.getElementById('chatModel').value;
            const message = document.getElementById('chatMessage').value;
            
            if (!token || !message) {
                alert('请填写Token和消息内容');
                return;
            }
            
            showLoading('chatLoading', 'chatResponse');
            
            try {
                const apiKey = getNextApiKey();
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:streamGenerateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: message
                            }]
                        }]
                    })
                });
                
                document.getElementById('chatLoading').style.display = 'none';
                const responseEl = document.getElementById('chatResponse');
                responseEl.style.display = 'block';
                responseEl.textContent = '🔄 流式响应:\n\n';
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                                    const text = data.candidates[0].content.parts[0].text;
                                    responseEl.textContent += text;
                                }
                            } catch (e) {
                                // 忽略解析错误
                            }
                        }
                    }
                }
            } catch (error) {
                hideLoading('chatLoading', 'chatResponse', `❌ 流式请求错误:\n\n${error.message}`);
            }
        }
        
        // 获取模型列表
        async function getModels() {
            const token = document.getElementById('modelsToken').value;

            if (!validateToken(token)) {
                return;
            }

            showLoading('modelsLoading', 'modelsResponse');

            try {
                const response = await fetch(`https://gemiban.pages.dev/api/v1/models`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                const data = await response.json();

                if (response.ok) {
                    const models = data.data.map(model => model.id).join('\n');
                    hideLoading('modelsLoading', 'modelsResponse', `✅ 可用模型:\n\n${models}`);
                } else {
                    hideLoading('modelsLoading', 'modelsResponse', `❌ 获取失败:\n\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                hideLoading('modelsLoading', 'modelsResponse', `❌ 网络错误:\n\n${error.message}`);
            }
        }
        
        // 直接调用测试
        async function testDirect() {
            const apiKey = document.getElementById('directKey').value;
            const message = document.getElementById('directMessage').value;

            if (!apiKey || !message) {
                alert('请填写API Key和消息内容');
                return;
            }

            // 显示安全警告
            showSecurityWarning();

            showLoading('directLoading', 'directResponse');

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: message
                            }]
                        }]
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.candidates && data.candidates.length > 0) {
                    const content = data.candidates[0].content.parts[0].text;
                    hideLoading('directLoading', 'directResponse', `✅ 直接调用成功:\n\n${content}`);
                } else {
                    hideLoading('directLoading', 'directResponse', `❌ 直接调用失败:\n\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                hideLoading('directLoading', 'directResponse', `❌ 网络错误:\n\n${error.message}`);
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Gemini Balance API 代理服务已加载');
            console.log('🔒 安全提示：请勿在此页面输入真实的API Keys');
            console.log('📧 如需获取访问权限，请联系管理员');
        });
    </script>
</body>
</html>
