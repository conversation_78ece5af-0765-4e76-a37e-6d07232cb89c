<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Balance - API代理服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #28a745;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .status-label {
            font-weight: 600;
            color: #333;
        }
        
        .status-value {
            color: #28a745;
            font-weight: 500;
        }
        
        .api-section {
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .api-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .endpoint {
            background: #f1f3f4;
            padding: 12px 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            margin-bottom: 10px;
            word-break: break-all;
        }
        
        .token-section {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .token-item {
            background: #fff;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            border: 1px solid #ddd;
        }
        
        .example-section {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 25px;
        }
        
        .code-block {
            background: #263238;
            color: #fff;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin-top: 15px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Gemini Balance</h1>
            <p>高性能Gemini API代理服务 - 负载均衡 & 无限免费</p>
        </div>
        
        <div class="status-card">
            <h3>📊 服务状态</h3>
            <div class="status-item">
                <span class="status-label">API Keys数量</span>
                <span class="status-value">2个</span>
            </div>
            <div class="status-item">
                <span class="status-label">预估每日请求</span>
                <span class="status-value">3,000次</span>
            </div>
            <div class="status-item">
                <span class="status-label">预估每小时请求</span>
                <span class="status-value">125次</span>
            </div>
            <div class="status-item">
                <span class="status-label">服务状态</span>
                <span class="status-value">🟢 运行中</span>
            </div>
        </div>
        
        <div class="api-section">
            <h3>🔗 API端点</h3>
            <p><strong>OpenAI兼容格式：</strong></p>
            <div class="endpoint">https://your-domain.com/v1</div>
            
            <p><strong>Gemini原生格式：</strong></p>
            <div class="endpoint">https://your-domain.com/gemini/v1beta</div>
            
            <p><strong>管理界面：</strong></p>
            <div class="endpoint">https://your-domain.com/keys_status</div>
        </div>
        
        <div class="token-section">
            <h3>🔑 访问令牌</h3>
            <div class="token-item">sk-gemiban-token-1 (普通用户)</div>
            <div class="token-item">sk-gemiban-token-2 (普通用户)</div>
            <div class="token-item">sk-gemiban-admin (管理员)</div>
        </div>
        
        <div class="example-section">
            <h3>📝 使用示例</h3>
            <p><strong>获取模型列表：</strong></p>
            <div class="code-block">curl -H "Authorization: Bearer sk-gemiban-token-1" \
     https://your-domain.com/v1/models</div>
            
            <p><strong>聊天完成：</strong></p>
            <div class="code-block">curl -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-gemiban-token-1" \
     -d '{
       "model": "gemini-1.5-flash",
       "messages": [{"role": "user", "content": "Hello!"}]
     }' \
     https://your-domain.com/v1/chat/completions</div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/keys_status" class="btn btn-success">📊 管理界面</a>
            <a href="https://github.com/lihaimings/gemiban" class="btn">📚 项目文档</a>
        </div>
    </div>
</body>
</html>
