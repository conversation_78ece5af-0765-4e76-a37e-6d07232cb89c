name = "gemini-balance"
main = "app/main.py"
compatibility_date = "2024-01-01"

[env.production]
name = "gemini-balance-prod"

[vars]
DATABASE_TYPE = "sqlite"
SQLITE_DATABASE = "/app/data/gemini_balance.db"
TEST_MODEL = "gemini-1.5-flash"
BASE_URL = "https://generativelanguage.googleapis.com/v1beta"
MAX_FAILURES = "3"
MAX_RETRIES = "3"
CHECK_INTERVAL_HOURS = "1"
TIMEZONE = "Asia/Shanghai"
TIME_OUT = "300"
LOG_LEVEL = "info"
AUTO_DELETE_ERROR_LOGS_ENABLED = "true"
AUTO_DELETE_ERROR_LOGS_DAYS = "7"
FAKE_STREAM_ENABLED = "true"
FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS = "5"

# 这些敏感信息需要通过 wrangler secret 命令设置
# wrangler secret put API_KEYS
# wrangler secret put ALLOWED_TOKENS  
# wrangler secret put AUTH_TOKEN

[[kv_namespaces]]
binding = "GEMINI_BALANCE_KV"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"

[[r2_buckets]]
binding = "GEMINI_BALANCE_STORAGE"
bucket_name = "gemini-balance-data"
