#!/usr/bin/env python3
"""
测试Gemini API Keys是否有效
"""

import requests
import json
import time

# 您的两个API Keys
API_KEYS = [
    "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ",
    "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
]

def test_api_key(api_key, key_index):
    """测试单个API Key"""
    print(f"\n🔑 测试 API Key {key_index + 1}: {api_key[:20]}...")
    
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
    headers = {"Content-Type": "application/json"}
    data = {
        "contents": [{"parts": [{"text": "Hello, please respond with 'API Key is working!'"}]}]
    }
    
    try:
        response = requests.post(
            f"{url}?key={api_key}",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ API Key {key_index + 1} 有效")
                print(f"   响应: {content.strip()}")
                return True
            else:
                print(f"❌ API Key {key_index + 1} 响应格式异常")
                print(f"   响应: {result}")
                return False
        else:
            print(f"❌ API Key {key_index + 1} 无效")
            print(f"   状态码: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API Key {key_index + 1} 测试失败")
        print(f"   错误: {e}")
        return False

def get_quota_info(api_key, key_index):
    """获取API Key的配额信息"""
    print(f"\n📊 获取 API Key {key_index + 1} 配额信息...")
    
    # 注意：Gemini API目前没有直接的配额查询接口
    # 这里返回默认的免费配额信息
    quota_info = {
        "model": "gemini-1.5-flash",
        "requests_per_minute": 15,
        "requests_per_day": 1500,
        "tokens_per_minute": 1000000,
        "status": "active"
    }
    
    print(f"   模型: {quota_info['model']}")
    print(f"   每分钟请求: {quota_info['requests_per_minute']}")
    print(f"   每日请求: {quota_info['requests_per_day']}")
    print(f"   每分钟Token: {quota_info['tokens_per_minute']:,}")
    
    return quota_info

def main():
    """主函数"""
    print("🚀 Gemini API Keys 测试工具")
    print("=" * 50)
    
    valid_keys = []
    invalid_keys = []
    
    # 测试每个API Key
    for i, key in enumerate(API_KEYS):
        if test_api_key(key, i):
            valid_keys.append(key)
            get_quota_info(key, i)
        else:
            invalid_keys.append(key)
        
        # 避免请求过快
        if i < len(API_KEYS) - 1:
            time.sleep(2)
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    print(f"✅ 有效 API Keys: {len(valid_keys)}")
    print(f"❌ 无效 API Keys: {len(invalid_keys)}")
    
    if valid_keys:
        print(f"\n📈 预估性能（基于 {len(valid_keys)} 个有效Keys）:")
        print(f"   每分钟请求: {len(valid_keys) * 15}")
        print(f"   每小时请求: {len(valid_keys) * 15 * 60}")
        print(f"   每日请求: {len(valid_keys) * 1500}")
        print(f"   每月请求: {len(valid_keys) * 1500 * 30:,}")
        
        print(f"\n🎯 推荐配置:")
        print(f"   Docker部署: 使用 docker-compose-simple.yml")
        print(f"   Cloudflare部署: 使用 wrangler.toml")
        print(f"   访问令牌: sk-gemiban-token-1, sk-gemiban-token-2")
        print(f"   管理员令牌: sk-gemiban-admin")
        
        print(f"\n🔗 API端点:")
        print(f"   OpenAI格式: http://localhost:8000/v1")
        print(f"   Gemini格式: http://localhost:8000/gemini/v1beta")
        print(f"   管理界面: http://localhost:8000/keys_status")
        
    else:
        print("\n❌ 没有有效的API Keys，请检查:")
        print("   1. API Keys是否正确")
        print("   2. 网络连接是否正常")
        print("   3. Google AI Studio是否可访问")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
