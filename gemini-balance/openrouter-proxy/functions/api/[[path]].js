// OpenRouter API 代理 - Cloudflare Pages Functions
// 支持多Key轮换和负载均衡

// 您的OpenRouter API Keys
const OPENROUTER_KEYS = [
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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",
  "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"
];

// 自定义访问令牌
const ALLOWED_TOKENS = [
  "sk-openrouter-proxy-1",
  "sk-openrouter-proxy-2", 
  "sk-openrouter-proxy-admin"
];

// Key使用统计
let keyUsageStats = {};

// 获取下一个可用的API Key
function getNextApiKey() {
  const now = Date.now();
  let bestKey = OPENROUTER_KEYS[0];
  let minUsage = Infinity;
  
  for (const key of OPENROUTER_KEYS) {
    const usage = keyUsageStats[key] || { count: 0, lastUsed: 0 };
    
    // 如果超过1小时没使用，重置计数
    if (now - usage.lastUsed > 3600000) {
      usage.count = 0;
    }
    
    if (usage.count < minUsage) {
      minUsage = usage.count;
      bestKey = key;
    }
  }
  
  // 更新使用统计
  if (!keyUsageStats[bestKey]) {
    keyUsageStats[bestKey] = { count: 0, lastUsed: 0 };
  }
  keyUsageStats[bestKey].count++;
  keyUsageStats[bestKey].lastUsed = now;
  
  return bestKey;
}

// 验证访问令牌
function validateToken(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return ALLOWED_TOKENS.includes(token);
}

// 处理CORS
function handleCORS(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
      },
    });
  }
  return null;
}

// 主要的请求处理函数
export async function onRequest(context) {
  const { request } = context;
  const url = new URL(request.url);
  const path = url.pathname;
  
  // 处理CORS
  const corsResponse = handleCORS(request);
  if (corsResponse) return corsResponse;
  
  // 验证访问令牌
  if (!validateToken(request)) {
    return new Response(JSON.stringify({ 
      error: 'Unauthorized', 
      message: 'Invalid or missing authorization token' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
  
  try {
    // 路由处理
    if (path.includes('/status')) {
      // 返回服务状态
      const status = {
        status: 'running',
        service: 'OpenRouter Proxy',
        api_keys_count: OPENROUTER_KEYS.length,
        estimated_daily_requests: OPENROUTER_KEYS.length * 200, // OpenRouter每个Key约200次/天
        key_usage_stats: keyUsageStats,
        supported_models: [
          'deepseek/deepseek-chat',
          'google/gemini-flash-1.5', 
          'meta-llama/llama-3.2-3b-instruct:free',
          'microsoft/phi-3-mini-128k-instruct:free',
          'qwen/qwen-2-7b-instruct:free'
        ]
      };
      
      return new Response(JSON.stringify(status), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
    
    // 代理所有其他请求到OpenRouter
    const apiKey = getNextApiKey();
    const openrouterUrl = `https://openrouter.ai/api${path}${url.search}`;
    
    // 构建请求头
    const headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://gemiban.pages.dev',
      'X-Title': 'Gemiban OpenRouter Proxy'
    };
    
    // 如果是POST请求，获取请求体
    let body = null;
    if (request.method === 'POST') {
      body = await request.text();
    }
    
    // 转发请求到OpenRouter
    const response = await fetch(openrouterUrl, {
      method: request.method,
      headers: headers,
      body: body,
    });
    
    // 获取响应数据
    const responseData = await response.text();
    
    // 返回响应
    return new Response(responseData, {
      status: response.status,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
    
  } catch (error) {
    console.error('Proxy Error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal Server Error', 
      message: error.message 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
