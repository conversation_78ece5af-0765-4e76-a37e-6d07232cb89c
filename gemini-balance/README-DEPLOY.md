# Gemini-Balance 私有部署指南

## 🎯 项目概述

这是基于 [snailyp/gemini-balance](https://github.com/snailyp/gemini-balance) 的私有部署版本，已配置好您的两个 Gemini API Keys，可以实现负载均衡和无限免费使用。

### 📊 当前配置
- **API Keys**: 2个 Gemini API Keys
- **预估性能**: 每日3,000次请求，每小时125次请求
- **访问令牌**: 3个（2个普通 + 1个管理员）
- **数据库**: SQLite（简化部署）

## 🚀 快速部署

### 方式1：一键部署（推荐）
```bash
# 运行快速部署脚本
./quick-deploy.sh
```

### 方式2：手动部署
```bash
# 创建数据目录
mkdir -p ./data

# 启动服务
docker-compose -f docker-compose-simple.yml up -d

# 查看状态
docker-compose -f docker-compose-simple.yml ps
```

## 🌐 访问信息

### API 端点
- **OpenAI 兼容格式**: `http://localhost:8000/v1`
- **Gemini 原生格式**: `http://localhost:8000/gemini/v1beta`

### 访问令牌
- `sk-gemiban-token-1` - 普通用户令牌1
- `sk-gemiban-token-2` - 普通用户令牌2  
- `sk-gemiban-admin` - 管理员令牌

### 管理界面
- **URL**: `http://localhost:8000/keys_status`
- **登录**: 使用 `sk-gemiban-admin` 令牌

## 📝 API 使用示例

### 获取模型列表
```bash
curl -H "Authorization: Bearer sk-gemiban-token-1" \
     http://localhost:8000/v1/models
```

### 聊天完成
```bash
curl -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-gemiban-token-1" \
     -d '{
       "model": "gemini-1.5-flash",
       "messages": [{"role": "user", "content": "Hello!"}]
     }' \
     http://localhost:8000/v1/chat/completions
```

### 流式聊天
```bash
curl -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-gemiban-token-1" \
     -d '{
       "model": "gemini-1.5-flash",
       "messages": [{"role": "user", "content": "写一首诗"}],
       "stream": true
     }' \
     http://localhost:8000/v1/chat/completions
```

## 🔧 管理命令

### 查看服务状态
```bash
docker-compose -f docker-compose-simple.yml ps
```

### 查看实时日志
```bash
docker-compose -f docker-compose-simple.yml logs -f
```

### 重启服务
```bash
docker-compose -f docker-compose-simple.yml restart
```

### 停止服务
```bash
docker-compose -f docker-compose-simple.yml down
```

### 更新服务
```bash
docker-compose -f docker-compose-simple.yml pull
docker-compose -f docker-compose-simple.yml up -d
```

## 🌍 Cloudflare 部署

如需部署到 Cloudflare Workers，请参考 `cloudflare-deploy.md` 文件。

### 快速 Cloudflare 部署
```bash
# 安装 Wrangler CLI
npm install -g wrangler

# 登录 Cloudflare
wrangler login

# 设置密钥
wrangler secret put API_KEYS
wrangler secret put ALLOWED_TOKENS
wrangler secret put AUTH_TOKEN

# 部署
wrangler deploy
```

## 📊 性能监控

### 查看 Key 状态
访问 `http://localhost:8000/keys_status` 可以看到：
- 每个 API Key 的使用情况
- 请求成功/失败统计
- 实时负载均衡状态

### 日志分析
```bash
# 查看错误日志
docker-compose -f docker-compose-simple.yml logs | grep ERROR

# 查看请求统计
docker-compose -f docker-compose-simple.yml logs | grep "Request completed"
```

## 🔒 安全配置

### 更改访问令牌
编辑 `docker-compose-simple.yml` 文件中的 `ALLOWED_TOKENS` 和 `AUTH_TOKEN`：

```yaml
environment:
  ALLOWED_TOKENS: '["your-new-token-1","your-new-token-2"]'
  AUTH_TOKEN: your-new-admin-token
```

### 添加更多 API Keys
编辑 `API_KEYS` 环境变量：

```yaml
environment:
  API_KEYS: '["key1","key2","key3","key4"]'
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   lsof -i :8000
   
   # 查看详细错误
   docker-compose -f docker-compose-simple.yml logs
   ```

2. **API 调用失败**
   ```bash
   # 检查 API Keys 是否有效
   curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=YOUR_API_KEY"
   ```

3. **权限错误**
   ```bash
   # 检查数据目录权限
   ls -la ./data
   
   # 修复权限
   sudo chown -R $USER:$USER ./data
   ```

## 📈 扩展配置

### 添加更多功能
编辑 `docker-compose-simple.yml` 可以启用：
- 图像生成功能
- 网络搜索功能
- 代理支持
- 更多模型支持

### 集成到其他项目
将此服务作为 OpenAI API 的替代品：

```python
import openai

client = openai.OpenAI(
    api_key="sk-gemiban-token-1",
    base_url="http://localhost:8000/v1"
)

response = client.chat.completions.create(
    model="gemini-1.5-flash",
    messages=[{"role": "user", "content": "Hello!"}]
)
```

## 🎉 完成！

现在您已经拥有了一个完全私有的、支持负载均衡的 Gemini API 代理服务！
