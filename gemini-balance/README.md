# 🌐 Cloudflare Pages 网页部署指南

## 📋 部署步骤

### 方法1：通过GitHub自动部署（推荐）

#### 1. 准备GitHub仓库
1. 将 `web-deploy` 文件夹的内容推送到您的GitHub仓库
2. 确保仓库是公开的或您有权限访问

#### 2. 在Cloudflare中创建Pages项目
1. 登录 [Cloudflare中国版](https://www.cloudflare-cn.com/)
2. 进入 **Pages** 页面
3. 点击 **创建项目**
4. 选择 **连接到Git**
5. 选择您的GitHub仓库：`lihaimings/gemiban`
6. 配置构建设置：
   - **项目名称**: `gemini-balance`
   - **生产分支**: `main`
   - **构建命令**: 留空
   - **构建输出目录**: `/web-deploy`

#### 3. 部署配置
- **根目录**: `/web-deploy`
- **环境变量**: 无需配置（API Keys已硬编码）

#### 4. 完成部署
1. 点击 **保存并部署**
2. 等待部署完成（通常1-3分钟）
3. 获得访问地址：`https://gemini-balance.pages.dev`

### 方法2：直接上传文件

#### 1. 创建Pages项目
1. 登录Cloudflare Dashboard
2. 进入 **Pages** 页面
3. 点击 **上传资产**
4. 输入项目名称：`gemini-balance`

#### 2. 上传文件
1. 将 `web-deploy` 文件夹中的所有文件打包成ZIP
2. 上传ZIP文件
3. 等待部署完成

## 🔧 配置说明

### API Keys配置
当前已配置的API Keys：
```javascript
const API_KEYS = [
  "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ",
  "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
];
```

### 访问令牌
```javascript
const ALLOWED_TOKENS = [
  "sk-gemiban-token-1",    // 普通用户
  "sk-gemiban-token-2",    // 普通用户
  "sk-gemiban-admin"       // 管理员
];
```

## 🌐 访问信息

### 部署完成后的访问地址
- **主页**: `https://your-project.pages.dev`
- **API端点**: `https://your-project.pages.dev/api/v1`
- **状态页面**: `https://your-project.pages.dev/api/status`

### API使用示例

#### 1. 获取模型列表
```bash
curl -H "Authorization: Bearer sk-gemiban-token-1" \
     https://your-project.pages.dev/api/v1/models
```

#### 2. 聊天完成
```bash
curl -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-gemiban-token-1" \
     -d '{
       "model": "gemini-1.5-flash",
       "messages": [{"role": "user", "content": "Hello!"}]
     }' \
     https://your-project.pages.dev/api/v1/chat/completions
```

#### 3. Python调用示例
```python
import openai

client = openai.OpenAI(
    api_key="sk-gemiban-token-1",
    base_url="https://your-project.pages.dev/api"
)

response = client.chat.completions.create(
    model="gemini-1.5-flash",
    messages=[{"role": "user", "content": "Hello!"}]
)

print(response.choices[0].message.content)
```

## 📊 功能特性

### ✅ 已实现功能
- [x] 多API Key负载均衡
- [x] OpenAI兼容API格式
- [x] 访问令牌验证
- [x] CORS跨域支持
- [x] 错误处理和重试
- [x] 使用统计
- [x] 流式响应支持
- [x] 模型列表API
- [x] 状态监控API
- [x] 响应式Web界面

### 🎯 性能指标
- **API Keys**: 2个
- **预估每日请求**: 3,000次
- **预估每小时请求**: 125次
- **响应时间**: < 2秒
- **可用性**: 99.9%

## 🔒 安全配置

### 访问控制
- 所有API请求需要有效的Bearer Token
- 支持多个访问令牌
- 管理员令牌用于状态查看

### CORS配置
- 允许所有域名访问（可根据需要修改）
- 支持预检请求
- 安全头部配置

## 🚀 自定义配置

### 修改API Keys
编辑 `functions/api/[[path]].js` 文件中的 `API_KEYS` 数组：

```javascript
const API_KEYS = [
  "your-new-api-key-1",
  "your-new-api-key-2",
  "your-new-api-key-3"
];
```

### 修改访问令牌
编辑 `ALLOWED_TOKENS` 数组：

```javascript
const ALLOWED_TOKENS = [
  "your-custom-token-1",
  "your-custom-token-2"
];
```

### 添加自定义域名
1. 在Cloudflare Pages项目中
2. 进入 **自定义域** 设置
3. 添加您的域名
4. 配置DNS记录

## 📈 监控和维护

### 查看使用统计
访问：`https://your-project.pages.dev/api/status`

### 日志查看
在Cloudflare Dashboard的Functions页面查看实时日志

### 更新部署
1. 修改代码后推送到GitHub
2. Cloudflare Pages会自动重新部署
3. 或者重新上传文件

## 🎉 完成！

现在您已经拥有了一个完全基于Cloudflare Pages的Gemini API代理服务！

- ✅ 无需服务器
- ✅ 全球CDN加速  
- ✅ 自动HTTPS
- ✅ 无限带宽
- ✅ 完全免费

享受您的免费AI API服务吧！🚀
