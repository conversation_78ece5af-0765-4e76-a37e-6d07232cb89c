#!/bin/bash

echo "🚀 Gemini-Balance 快速部署脚本"
echo "================================"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建数据目录
echo "📁 创建数据目录..."
mkdir -p ./data

# 设置权限
chmod 755 ./data

# 使用简化的docker-compose配置启动服务
echo "🐳 启动 Gemini-Balance 服务..."
docker-compose -f docker-compose-simple.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if docker-compose -f docker-compose-simple.yml ps | grep -q "Up"; then
    echo "✅ 服务启动成功！"
    echo ""
    echo "🌐 访问信息："
    echo "   API 端点: http://localhost:8000"
    echo "   管理界面: http://localhost:8000/keys_status"
    echo "   管理员令牌: sk-gemiban-admin"
    echo ""
    echo "📝 API 使用示例："
    echo "   curl -H 'Authorization: Bearer sk-gemiban-token-1' \\"
    echo "        http://localhost:8000/v1/models"
    echo ""
    echo "🔧 管理命令："
    echo "   查看日志: docker-compose -f docker-compose-simple.yml logs -f"
    echo "   停止服务: docker-compose -f docker-compose-simple.yml down"
    echo "   重启服务: docker-compose -f docker-compose-simple.yml restart"
else
    echo "❌ 服务启动失败，请检查日志："
    docker-compose -f docker-compose-simple.yml logs
fi

echo ""
echo "📊 当前配置："
echo "   API Keys: 2个"
echo "   预估每日请求: 3,000次"
echo "   预估每小时请求: 125次"
echo ""
echo "🎉 部署完成！"
