#!/usr/bin/env python3
"""
最终配置验证 - 测试最佳模型配置
"""

import requests
import json
import time

def test_best_models():
    """测试最佳模型配置"""
    print("🚀 测试最佳OpenRouter模型配置")
    print("=" * 50)
    
    # 最佳模型配置（按优先级排序）
    best_models = [
        "meta-llama/llama-3.2-3b-instruct:free",
        "google/gemma-2-9b-it:free", 
        "qwen/qwen-2.5-coder-32b-instruct:free",
        "meta-llama/llama-3.3-70b-instruct:free",
        "mistralai/mistral-nemo:free"
    ]
    
    # 测试用的API Keys（前5个）
    test_keys = [
        "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",
        "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",
        "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",
        "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",
        "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"
    ]
    
    results = {}
    
    for model in best_models:
        print(f"\n🤖 测试模型: {model}")
        results[model] = {"valid_keys": 0, "response_quality": []}
        
        for i, key in enumerate(test_keys):
            try:
                response = requests.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "https://aiwritex.com",
                        "X-Title": "AIWriteX Final Test"
                    },
                    json={
                        "model": model,
                        "messages": [{"role": "user", "content": "写一段关于人工智能发展的简短文章，100字左右。"}],
                        "max_tokens": 200
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'choices' in data and len(data['choices']) > 0:
                        content = data['choices'][0]['message']['content']
                        results[model]["valid_keys"] += 1
                        results[model]["response_quality"].append(len(content))
                        print(f"   ✅ Key {i+1} 可用 - 响应长度: {len(content)}字符")
                    else:
                        print(f"   ❌ Key {i+1} 响应格式异常")
                else:
                    print(f"   ❌ Key {i+1} 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ Key {i+1} 错误: {str(e)[:50]}")
            
            time.sleep(1)
    
    return results

def analyze_results(results):
    """分析测试结果"""
    print("\n" + "=" * 50)
    print("📊 最终配置分析")
    print("=" * 50)
    
    best_model = None
    max_valid_keys = 0
    
    for model, result in results.items():
        valid_count = result["valid_keys"]
        avg_response_length = sum(result["response_quality"]) / len(result["response_quality"]) if result["response_quality"] else 0
        
        print(f"\n🤖 {model}")
        print(f"   ✅ 可用Keys: {valid_count}/5")
        print(f"   📝 平均响应长度: {avg_response_length:.0f}字符")
        
        if valid_count > max_valid_keys:
            max_valid_keys = valid_count
            best_model = model
    
    return best_model, max_valid_keys

def generate_final_config(best_model, valid_keys_count):
    """生成最终配置建议"""
    print(f"\n🎯 最终配置建议")
    print("=" * 50)
    
    if valid_keys_count >= 4:
        status = "🟢 优秀"
        recommendation = "配置完美，可以开始使用AIWriteX"
    elif valid_keys_count >= 2:
        status = "🟡 良好"
        recommendation = "配置可用，建议监控使用情况"
    else:
        status = "🔴 需要改进"
        recommendation = "建议检查API Keys或尝试其他模型"
    
    print(f"配置状态: {status}")
    print(f"推荐模型: {best_model}")
    print(f"可用Keys: {valid_keys_count}/5")
    print(f"预估总可用Keys: {valid_keys_count * 4} (基于19个Keys)")
    print(f"建议: {recommendation}")
    
    print(f"\n🔧 AIWriteX 启动步骤:")
    print("1. 确认配置文件已更新")
    print("2. 启动AIWriteX主程序")
    print("3. 选择OpenRouter作为API类型")
    print(f"4. 使用模型: {best_model}")
    print("5. 开始创作文章")
    
    print(f"\n📈 性能预期:")
    daily_requests = valid_keys_count * 4 * 200  # 每个Key约200次/天
    print(f"   每日文章生成: {daily_requests // 10} 篇 (假设每篇10次请求)")
    print(f"   每小时文章: {daily_requests // 10 // 24} 篇")
    print(f"   响应时间: 2-5秒")

def main():
    """主函数"""
    print("🎉 AIWriteX 最终配置验证")
    print("=" * 50)
    
    # 测试最佳模型
    results = test_best_models()
    
    # 分析结果
    best_model, valid_keys_count = analyze_results(results)
    
    # 生成最终配置
    generate_final_config(best_model, valid_keys_count)
    
    print(f"\n✨ 配置完成！您的AIWriteX已准备就绪！")

if __name__ == "__main__":
    main()
