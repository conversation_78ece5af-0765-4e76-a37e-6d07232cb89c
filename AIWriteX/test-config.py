#!/usr/bin/env python3
"""
AIWriteX 配置测试脚本
测试API Keys和微信配置是否正确
"""

import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_openrouter_keys():
    """测试OpenRouter API Keys"""
    print("🔑 测试 OpenRouter API Keys...")
    
    openrouter_keys = [
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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",
        "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"
    ]

    valid_keys = 0
    total_keys = len(openrouter_keys)

    for i, key in enumerate(openrouter_keys):  # 测试所有Keys
        print(f"\n🔍 测试 Key {i+1}: {key[:20]}...")
        
        try:
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://aiwritex.com",
                    "X-Title": "AIWriteX Test"
                },
                json={
                    "model": "deepseek/deepseek-chat-v3-0324:free",
                    "messages": [{"role": "user", "content": "Hello, test message"}],
                    "max_tokens": 50
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    content = data['choices'][0]['message']['content']
                    print(f"✅ Key {i+1} 有效")
                    print(f"   响应: {content[:50]}...")
                    valid_keys += 1
                else:
                    print(f"❌ Key {i+1} 响应格式异常")
            else:
                print(f"❌ Key {i+1} 无效 - 状态码: {response.status_code}")
                print(f"   错误: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ Key {i+1} 测试失败: {e}")
        
        # 避免请求过快
        time.sleep(1)

    print(f"\n📊 OpenRouter 测试结果: {valid_keys}/{total_keys} 个Key有效")
    return valid_keys

def test_gemini_keys():
    """测试Gemini API Keys"""
    print("\n🔑 测试 Gemini API Keys...")
    
    gemini_keys = [
        "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ",
        "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
    ]
    
    valid_keys = 0
    
    for i, key in enumerate(gemini_keys):
        print(f"\n🔍 测试 Gemini Key {i+1}: {key[:20]}...")
        
        try:
            response = requests.post(
                f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={key}",
                headers={"Content-Type": "application/json"},
                json={
                    "contents": [{
                        "parts": [{"text": "Hello, test message"}]
                    }]
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'candidates' in data and len(data['candidates']) > 0:
                    content = data['candidates'][0]['content']['parts'][0]['text']
                    print(f"✅ Gemini Key {i+1} 有效")
                    print(f"   响应: {content[:50]}...")
                    valid_keys += 1
                else:
                    print(f"❌ Gemini Key {i+1} 响应格式异常")
            else:
                print(f"❌ Gemini Key {i+1} 无效 - 状态码: {response.status_code}")
                print(f"   错误: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ Gemini Key {i+1} 测试失败: {e}")
        
        time.sleep(2)
    
    print(f"\n📊 Gemini 测试结果: {valid_keys}/2 个Key有效")
    return valid_keys

def test_wechat_config():
    """测试微信配置"""
    print("\n📱 测试微信配置...")
    
    wechat_config = {
        "appid": "wx9538fd0990debc8f",
        "appsecret": "b08b41349d87b3ad6d34340436e386e1",
        "author": "ljli"
    }
    
    print(f"AppID: {wechat_config['appid']}")
    print(f"AppSecret: {wechat_config['appsecret'][:10]}...")
    print(f"Author: {wechat_config['author']}")
    
    # 测试获取access_token
    try:
        response = requests.get(
            f"https://api.weixin.qq.com/cgi-bin/token",
            params={
                "grant_type": "client_credential",
                "appid": wechat_config['appid'],
                "secret": wechat_config['appsecret']
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'access_token' in data:
                print("✅ 微信配置有效")
                print(f"   Access Token: {data['access_token'][:20]}...")
                print(f"   过期时间: {data.get('expires_in', 'unknown')}秒")
                return True
            else:
                print("❌ 微信配置无效")
                print(f"   错误: {data}")
                return False
        else:
            print(f"❌ 微信API请求失败 - 状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 微信配置测试失败: {e}")
        return False

def test_config_files():
    """测试配置文件是否存在和格式正确"""
    print("\n📄 检查配置文件...")
    
    config_files = [
        "src/ai_write_x/config/config.yaml",
        "src/ai_write_x/config/aipyapp.toml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} 存在")
            
            # 检查文件大小
            size = os.path.getsize(config_file)
            print(f"   文件大小: {size} 字节")
            
            # 检查是否包含配置的API Keys
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if '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' in content:
                print("   ✅ 包含OpenRouter API Key")
            else:
                print("   ❌ 未找到OpenRouter API Key")
                
            if 'AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ' in content:
                print("   ✅ 包含Gemini API Key")
            else:
                print("   ❌ 未找到Gemini API Key")
                
            if 'wx9538fd0990debc8f' in content:
                print("   ✅ 包含微信AppID")
            else:
                print("   ❌ 未找到微信AppID")
        else:
            print(f"❌ {config_file} 不存在")

def main():
    """主函数"""
    print("🚀 AIWriteX 配置测试工具")
    print("=" * 60)
    
    # 测试配置文件
    test_config_files()
    
    # 测试API Keys
    openrouter_valid = test_openrouter_keys()
    gemini_valid = test_gemini_keys()
    
    # 测试微信配置
    wechat_valid = test_wechat_config()
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    print(f"🔑 OpenRouter Keys: {openrouter_valid}/19 有效")
    print(f"🔑 Gemini Keys: {gemini_valid}/2 有效")
    print(f"📱 微信配置: {'✅ 有效' if wechat_valid else '❌ 无效'}")
    
    total_keys = openrouter_valid + gemini_valid
    print(f"\n📊 总计可用API Keys: {total_keys}")
    
    if total_keys >= 3 and wechat_valid:
        print("\n🎉 配置完成！AIWriteX已准备就绪")
        print("\n🚀 推荐使用:")
        print("   - 主要API: OpenRouter (国内直连)")
        print("   - 备用API: Gemini (需要代理)")
        print("   - 微信发布: 已配置")
    else:
        print("\n⚠️  配置需要完善:")
        if total_keys < 3:
            print("   - 建议至少配置3个有效的API Keys")
        if not wechat_valid:
            print("   - 微信配置需要检查")
    
    print("\n🎯 下一步:")
    print("   1. 运行 AIWriteX 主程序")
    print("   2. 在界面中选择 OpenRouter 作为主要API")
    print("   3. 开始创作和发布文章")

if __name__ == "__main__":
    main()
