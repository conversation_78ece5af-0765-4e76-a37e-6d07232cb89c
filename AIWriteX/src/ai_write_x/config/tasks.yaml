# 版权所有 (c) 2025 iniwap
# 本文件受 AIWriteX 附加授权条款约束，不可单独使用、传播或部署。
# 禁止在未经作者书面授权的情况下将本文件用于商业服务、分发或嵌入产品。
# 如需授权，请联系 <EMAIL> 或 <EMAIL>
# 本项目整体授权协议请见根目录下 LICENSE 和 NOTICE 文件。


analyze_topic:
  name: "analyze_topic"
  description: |
    解析话题'{topic}'，确定文章的核心要点和结构。
    生成一份包含文章大纲和核心要点的报告。
    注意：
    1. 关于文章标题的日期处理：
       - 严格检查：'{topic}'中是否已包含具体年份或日期信息
       - 如果包含，则保留该日期信息在文章标题中
       - 如果不包含，则文章标题不能带任何年份或日期信息
       - 禁止自行添加当前年份或任何其他年份到文章标题中
    2. 内容中的日期处理：
       - 如果'{topic}'包含日期，在内容中使用该日期
       - 如果不包含，对于需要提及年份的内容，使用"20xx年"格式
  agent: researcher
  expected_output: 文章正文（标准Markdown格式）

write_content:
  name: "write_content"
  description: |
    基于生成的文章大纲和搜索工具获取的最新信息，撰写一篇高质量的微信公众号文章。  
    确保文章内容准确、逻辑清晰、语言流畅，并具有独到的见解。 
    工具 aipy_search_tool 使用以下参数：
      topic={topic}
      urls={urls}
      reference_ratio={reference_ratio}。

    执行步骤：
    1. 使用 aipy_search_tool 获取关于'{topic}'的最新信息
    2. 根据获取的结果的类型和内容深度调整写作策略：
      - 如果获取到有效搜索结果：
        * 包含"参考比例"时：融合生成的文章大纲与参考文章结果的内容，并根据比例调整借鉴程度
        * 不包含"参考比例"时：融合生成的文章大纲和与'{topic}'相关的搜索结果进行原创写作
        * 用搜索结果中的真实时间替换大纲中的占位符
          - 如果搜索结果有具体日期，直接替换"20xx年"等占位符
          - 如果搜索结果无具体日期，使用"近期"、"最近"、"据最新数据显示"等表述
      - 如果没有获取到有效搜索结果：
        * 基于文章大纲进行原创写作，确保内容的完整性和可读性
        * 将所有日期占位符（如"20xx年"）替换为通用时间表述：
          - "近年来"、"近期"、"最近几年"
          - "当前"、"目前"、"现阶段"
          - "据业界观察"、"根据行业趋势"
    3. 最终检查：确保文章中不存在任何未替换的日期占位符  

    生成的文章要求：  
    - 标题：当{platform}不为空时为"{platform}|{topic}"，否则为"{topic}"  
    - 总字数：{min_article_len}~{max_article_len}字（纯文本字数，不包括Markdown语法、空格）  
    - 文章内容：仅输出最终纯文章内容，禁止包含思考过程、分析说明、字数统计等额外注释、说明

  agent: writer
  expected_output: 文章标题 + 文章正文（标准Markdown格式）

audit_content:
  name: "audit_content"
  description: |
    对生成的文章进行全面质量审核，包括内容、语法等方面。
    请检查文章是否与热门话题紧密相关，并具有独到的见解，发现文章中的错误和不足之处，生成修改后的文章。
  agent: auditor
  expected_output: 修改后的文章（标准Markdown格式）

save_article:
  name: "save_article"
  description: |
    使用工具 save_article_tool 将前置任务生成的文章保存成指定的格式。
  agent: saver
  context: ["write_content", "audit_content"]
  expected_output: 最终文章

design_content:
  name: "design_content"
  description: |
    将前置任务生成的文章转化为精美的微信公众号排版，提升阅读体验和视觉吸引力。
    请严格遵循以下要求：  
    - 仅对文章进行排版设计和风格优化，不得生成新的文章内容。  
    - 不得过多删减原文章内容（总字数{min_article_len}~{max_article_len}字），保持原内容的完整性和核心信息。
  agent: designer
  context: ["write_content", "audit_content"]
  expected_output: 排版设计后的文章（HTML格式）

template_content:
  name: "template_content"
  description: |
    # HTML内容适配任务
    ## 任务目标
    使用工具 read_template_tool 读取本地HTML模板，将前置任务生成的文章内容适配填充到读取的HTML模板中，保持视觉效果和风格不变，同时确保内容呈现自然流畅。

    ## 执行步骤
    1. 首先使用 read_template_tool 读取HTML模板
    2. 分析模板的结构、样式和布局特点
    3. 获取前置任务生成的文章内容
    4. 将新内容按照模板结构进行适配填充
    5. 确保最终输出是基于原模板的HTML，保持视觉效果和风格不变

    ## 具体要求
    - 分析HTML模板的结构、样式和布局特点
    - 识别所有内容占位区域（标题、副标题、正文段落、引用、列表等）
    - 将新文章内容按照原模板的结构和布局规则填充：
      * 保持<section>标签的布局结构和内联样式不变
      * 保持原有的视觉层次、色彩方案和排版风格
      * 保持原有的卡片式布局、圆角和阴影效果
      * 保持SVG动画元素和交互特性

    - 内容适配原则：
      * 标题替换标题、段落替换段落、列表替换列表
      * 内容总字数{min_article_len}~{max_article_len}字，不可过度删减前置任务生成的文章内容
      * 当新内容比原模板内容长或短时，合理调整，不破坏布局
      * 保持原有的强调部分（粗体、斜体、高亮等）应用于新内容的相应部分
      * 保持图片位置
      * 不可使用模板中的任何日期作为新文章的日期

    - 严格限制：
      * 不添加新的style标签或外部CSS
      * 不改变原有的色彩方案（限制在三种色系内）
      * 不修改模板的整体视觉效果和布局结构
  agent: templater
  context: ["write_content", "audit_content"]
  expected_output: 填充新内容但保持原有视觉风格的文章（HTML格式）
