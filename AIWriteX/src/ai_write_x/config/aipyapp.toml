workdir = "aipy_work"
record = true
max_tokens = 4096
default_llm_provider = "openrouter"

[llm.openrouter]
type = "openai"
model = "meta-llama/llama-3.2-3b-instruct:free"                                      # 最佳免费模型，5/5 Keys可用
api_key = "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"
base_url = "https://openrouter.ai/api/v1"
enable = true
default = true
timeout = 30
max_tokens = 8192

[llm.grok]
type = "openai"
model = "grok-3-mini"
api_key = ""
base_url = "https://api.x.ai/v1/"
enable = true
default = false
timeout = 30
max_tokens = 8192

[llm.qwen]
type = "openai"
model = "openai/qwen-max"
api_key = ""
base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
enable = true
default = false
timeout = 30
max_tokens = 8192

[llm.gemini]
type = "openai"
model = "gemini-1.5-flash"
api_key = "sk-gemiban-token-1"
base_url = "https://gemiban.pages.dev/api/v1"
enable = true
default = false
timeout = 30
max_tokens = 8192

[llm.ollama]
type = "ollama"
model = "llama3"
api_key = ""
base_url = "http://localhost:11434"
enable = true
default = false
timeout = 30
max_tokens = 8192

[llm.deepseek]
type = "deepseek"
model = "deepseek-chat"
api_key = ""
base_url = "https://api.deepseek.com"
enable = true
default = false
timeout = 30
max_tokens = 8192
