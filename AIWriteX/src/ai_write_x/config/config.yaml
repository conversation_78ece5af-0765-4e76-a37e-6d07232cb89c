platforms:
  - name: 微博
    weight: 0.3
  - name: 抖音
    weight: 0.20
  - name: 小红书
    weight: 0.12
  - name: 今日头条
    weight: 0.1
  - name: 百度热点
    weight: 0.08
  - name: 哔哩哔哩
    weight: 0.06
  - name: 快手
    weight: 0.05
  - name: 虎扑
    weight: 0.05
  - name: 豆瓣小组
    weight: 0.02
  - name: 澎湃新闻
    weight: 0.01
  - name: 知乎热榜
    weight: 0.01
wechat:
  credentials:
    - appid: "wx9538fd0990debc8f"
      appsecret: "b08b41349d87b3ad6d34340436e386e1"
      author: "ljli"
      call_sendall: false
      sendall: true
      tag_id: 0
    - appid: ""
      appsecret: ""
      author: ""
      call_sendall: false
      sendall: true
      tag_id: 0
    - appid: ""
      appsecret: ""
      author: ""
      call_sendall: false
      sendall: true
      tag_id: 0
api:
  api_type: OpenRouter
  Grok:
    key: XAI_API_KEY
    key_index: 0
    api_key:
      - ""
      - ""
    model_index: 0
    model:
      - xai/grok-3
    api_base: https://api.x.ai/v1/chat/completions
  Qwen:
    key: OPENAI_API_KEY
    key_index: 0
    api_key:
      - ""
      - ""
    model_index: 0
    model:
      - openai/qwen-plus
    api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
  Gemini:
    key: GEMINI_API_KEY
    key_index: 0
    api_key:
      - "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ"
      - "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
    model_index: 0
    model:
      - gemini-1.5-flash
      - gemini-1.5-pro
      - gemini-2.0-flash
    api_base: https://generativelanguage.googleapis.com/v1beta/openai/
  OpenRouter:
    key: OPENROUTER_API_KEY
    key_index: 0
    api_key:
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
      - "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"
    model_index: 0
    model:
      - meta-llama/llama-3.2-3b-instruct:free
      - google/gemma-2-9b-it:free
      - openrouter/auto
      - openrouter/deepseek/deepseek-chat-v3-0324:free
      - openrouter/deepseek/deepseek-r1-0528:free
      - openrouter/deepseek/deepseek-prover-v2:free
      - openrouter/deepseek/deepseek-r1:free
      - openrouter/deepseek/deepseek-chat:free
      - openrouter/qwen/qwen3-32b:free
      - openrouter/qwen/qwq-32b:free
    api_base: https://openrouter.ai/api/v1
  GeminiProxy:
    key: GEMINI_PROXY_TOKEN
    key_index: 0
    api_key:
      - "sk-gemiban-token-1"
      - "sk-gemiban-token-2"
      - "sk-gemiban-admin"
    model_index: 0
    model:
      - gemini-1.5-flash
      - gemini-1.5-pro
      - gemini-2.0-flash
    api_base: https://gemiban.pages.dev/api/v1
  Ollama:
    key: OPENAI_API_KEY
    key_index: 0
    api_key:
      - tmp-key
    model_index: 0
    model:
      - ollama/deepseek-r1:14b
      - ollama/deepseek-r1:7b
    api_base: http://localhost:11434
  Deepseek:
    key: DEEPSEEK_API_KEY
    key_index: 0
    api_key:
      - ""
    model_index: 0
    model:
      - deepseek/deepseek-chat
      - deepseek/deepseek-reasoner
    api_base: https://api.deepseek.com/v1
  SiliconFlow:
    key: OPENAI_API_KEY
    key_index: 0
    api_key:
      - ""
    model_index: 0
    model:
      - openai/deepseek-ai/DeepSeek-V3
      - openai/deepseek-ai/DeepSeek-R1
      - openai/Qwen/QwQ-32B
      - openai/Qwen/Qwen3-32B
    api_base: https://api.siliconflow.cn/v1
img_api:
  api_type: picsum
  ali:
    api_key: ""
    model: wanx2.0-t2i-turbo
  picsum:
    api_key: ""
    model: ""
use_template: true
template_category: ""
template: ""
need_auditor: false
use_compress: true
use_search_service: false
aipy_search_max_results: 10
aipy_search_min_results: 1
min_article_len: 1000
max_article_len: 2000
auto_publish: true
article_format: html # 仅支持：html、markdown、txt
format_publish: true
