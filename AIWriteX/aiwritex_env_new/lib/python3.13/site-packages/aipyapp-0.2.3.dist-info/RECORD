../../../bin/aipy,sha256=1SDiSLQL8NlGA6GykmHpMaHPcuxSvDdtcaT_Ue7zTZc,267
../../../bin/aipyw,sha256=yl1lMzs4YrNpTWsjqTJ6ndZ79R8Co4ELwuYdbiBQMf8,269
aipyapp-0.2.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
aipyapp-0.2.3.dist-info/METADATA,sha256=3r1AzOVZEJpgLr9j7yDrzvVpWUEYnxQKZNRmE78hkcM,5436
aipyapp-0.2.3.dist-info/RECORD,,
aipyapp-0.2.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
aipyapp-0.2.3.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
aipyapp-0.2.3.dist-info/entry_points.txt,sha256=XtVzF7ZYu9uaHIdXf1PzxqIvN35aKI6Z4G7cmAW8nCc,93
aipyapp-0.2.3.dist-info/licenses/LICENSE,sha256=nyY-cmFl05G-lh-ACfqdADu0jpEUF291LmAQzQ3mEpI,1027
aipyapp/__init__.py,sha256=w51dOKHQU3lELeKwpJxET17v4RaGTbB6vqg17I6cwyM,383
aipyapp/__main__.py,sha256=U64UCFzUBJK02X7Ez3t0pzneqC5SpyVHEGdIgw1__pI,2184
aipyapp/__pycache__/__init__.cpython-313.pyc,,
aipyapp/__pycache__/__main__.cpython-313.pyc,,
aipyapp/__pycache__/__version__.cpython-313.pyc,,
aipyapp/__pycache__/i18n.cpython-313.pyc,,
aipyapp/__pycache__/runtime.cpython-313.pyc,,
aipyapp/__version__.py,sha256=wD8hnA5gV5UmPkQnpT3xR6V2csgj9K5NEADogbLK79M,511
aipyapp/aipy/__init__.py,sha256=fRnYUV6Nkjk5SMJ5s1hTQTwKzL2QTBvZ310W_8U_YKQ,180
aipyapp/aipy/__pycache__/__init__.cpython-313.pyc,,
aipyapp/aipy/__pycache__/blocks.cpython-313.pyc,,
aipyapp/aipy/__pycache__/cache.cpython-313.pyc,,
aipyapp/aipy/__pycache__/config.cpython-313.pyc,,
aipyapp/aipy/__pycache__/diagnose.cpython-313.pyc,,
aipyapp/aipy/__pycache__/interface.cpython-313.pyc,,
aipyapp/aipy/__pycache__/libmcp.cpython-313.pyc,,
aipyapp/aipy/__pycache__/llm.cpython-313.pyc,,
aipyapp/aipy/__pycache__/mcp_tool.cpython-313.pyc,,
aipyapp/aipy/__pycache__/multimodal.cpython-313.pyc,,
aipyapp/aipy/__pycache__/plugin.cpython-313.pyc,,
aipyapp/aipy/__pycache__/prompt.cpython-313.pyc,,
aipyapp/aipy/__pycache__/runtime.cpython-313.pyc,,
aipyapp/aipy/__pycache__/task.cpython-313.pyc,,
aipyapp/aipy/__pycache__/taskmgr.cpython-313.pyc,,
aipyapp/aipy/__pycache__/tips.cpython-313.pyc,,
aipyapp/aipy/__pycache__/tool.cpython-313.pyc,,
aipyapp/aipy/__pycache__/trustoken.cpython-313.pyc,,
aipyapp/aipy/__pycache__/utils.cpython-313.pyc,,
aipyapp/aipy/__pycache__/wizard.cpython-313.pyc,,
aipyapp/aipy/blocks.py,sha256=8mRjBN-3g4oUuR2peYrstlYbF1AD-gODS0oOoXh9gkQ,7135
aipyapp/aipy/cache.py,sha256=9VaeIhe0tIa5sX4lZPvc0l9wvgE9L29xIoEJwlcTviI,9957
aipyapp/aipy/config.py,sha256=ZoGf0wAmaxJQMknoMXruewyRshDR1CsnhUq-7xSSH2Q,13544
aipyapp/aipy/diagnose.py,sha256=9CvxGm2Lgg162mSxC4R1RM5Uv-MqornVxcYWhYsZdo0,6928
aipyapp/aipy/interface.py,sha256=6fleY_sjiyDJfTMX5wj5SGTUTH770d2-BhBZ7kam1xo,950
aipyapp/aipy/libmcp.py,sha256=Ky3gpwaw037RYD9Rb123vEdmI1juhkm9TT_Uh1izTBs,13085
aipyapp/aipy/llm.py,sha256=ZkAw_i0gvlti_PrsEqIHyqfyuG5hlc5U1iTlPEukeY8,9293
aipyapp/aipy/mcp_tool.py,sha256=0NmGZtAQcmu0CVmPDoJTtBpaVWTx8HsQFnqzVHfJ5_Y,15809
aipyapp/aipy/multimodal.py,sha256=Am7yeso85lHbDG38IJrUdqjlYa28EprPTsIw-m6G5Ic,5890
aipyapp/aipy/plugin.py,sha256=Hb3Mk3r9ghFD93HXqBN_6qryLztqyEKAADZtikwpeGo,3038
aipyapp/aipy/prompt.py,sha256=103D9C4YtJxB51Tozn0_vjJIAVOd1QJ_q-tJ6Ezvqks,14237
aipyapp/aipy/runtime.py,sha256=oI2ZxmyC57xa9qsaRrMbxdTvH2WVKjdkwOoRibtaCkw,2748
aipyapp/aipy/task.py,sha256=CEw3F_rCEf6PCaVpAYpi5KWZ3sklq42IGKJgoQx0kXo,14620
aipyapp/aipy/taskmgr.py,sha256=sVAYwuhTsmcmLExumOh9WHpnU9v3lkPFVSj8g4e2bSg,5030
aipyapp/aipy/tips.py,sha256=GWA_v5G1DG4yJHGpguFLcOa29zanIIWZhOQz6unCNmI,4281
aipyapp/aipy/tool.py,sha256=DfTi5x7BowhltKjFYf11CduGKOLBkiYFFAAEBLdJZVU,6678
aipyapp/aipy/trustoken.py,sha256=lnrDYlzC5y0Bzgb6tH0e2lstMbvfhaViiTRzenGiyCg,6976
aipyapp/aipy/utils.py,sha256=4TnOr0y4FAN9ojYsAJT5PDtkHdT1bGfeQeGWj10ax-4,4050
aipyapp/aipy/wizard.py,sha256=sN0V05rSqdt3I_3f7L5Ac7TR22LskJczqhqVS_2Yy6A,3523
aipyapp/cli/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
aipyapp/cli/__pycache__/__init__.cpython-313.pyc,,
aipyapp/cli/__pycache__/cli_ipython.cpython-313.pyc,,
aipyapp/cli/__pycache__/cli_python.cpython-313.pyc,,
aipyapp/cli/__pycache__/cli_task.cpython-313.pyc,,
aipyapp/cli/cli_ipython.py,sha256=I6N6KzFdFV5YRPwBzcJIBOtk-FouwTHE9ySoie5aMaA,3818
aipyapp/cli/cli_python.py,sha256=wcSoxjXJRvijDOEle9hAZ72iDDSayXW8CpaK_lQV0Bs,2767
aipyapp/cli/cli_task.py,sha256=CrIjTRTvlXavLGIEw3nWEZYkNacBzsoC_wnvaacjMMg,6169
aipyapp/cli/command/__init__.py,sha256=NE2vaNAozwo6N-BTVYujYe4pfjX6xdrZ1LqkrAGQkSQ,108
aipyapp/cli/command/__pycache__/__init__.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/base.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_env.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_help.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_info.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_llm.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_mcp.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_task.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_tools.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/cmd_use.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/manager.cpython-313.pyc,,
aipyapp/cli/command/__pycache__/utils.cpython-313.pyc,,
aipyapp/cli/command/base.py,sha256=Qb1RL-rtHvz_-gvXeBagNN6HVqkeygccIN_bu_c6fzA,4039
aipyapp/cli/command/cmd_env.py,sha256=GnJo2wpGq4OWlvwrvZFXFNnmzh3-pVLgNs8F1ei9YrY,459
aipyapp/cli/command/cmd_help.py,sha256=AYdY3huVt3fMsYunHhW_mWoJKkyqS0WYSxCwrTrZBrU,1232
aipyapp/cli/command/cmd_info.py,sha256=0eS_8TR_gUi_hFAQx3oF0xpZroeCPR0UvAaRpv4Tsl8,858
aipyapp/cli/command/cmd_llm.py,sha256=Fi6qayH5xDjAIP2bnxxErl2aX87L0mXhpCLGWc46JjI,862
aipyapp/cli/command/cmd_mcp.py,sha256=yIICvlu1i1XC6NIY5ACXaReh9PCy7Dd6Q6nnJkVy4Vc,1713
aipyapp/cli/command/cmd_task.py,sha256=RMXQZehz09CrwsyIsAP9oKTq7Of-Yj6GzsFtaRnub0Q,1009
aipyapp/cli/command/cmd_tools.py,sha256=g554pXhW62_jSlBjCt_9ifs0FMdSaCNA7jF13NY8AG8,2159
aipyapp/cli/command/cmd_use.py,sha256=mXwIHkj8XW_AGeKoYm3bh9mF2PMgUuxqzrye0SkRZCU,948
aipyapp/cli/command/manager.py,sha256=dn4j_2rElS3j1J-rE4eOe7G0cHY5gsuOs5H3ycuB084,6362
aipyapp/cli/command/utils.py,sha256=pIYhvOCZYpp7TiVx0EKcAB8ID3be8YTyldU85HTFlcg,826
aipyapp/config/__init__.py,sha256=qbHXZhC-AwuMBS3mvU_deZeh0ge8aldrcyw95m3XIug,52
aipyapp/config/__pycache__/__init__.cpython-313.pyc,,
aipyapp/config/__pycache__/base.cpython-313.pyc,,
aipyapp/config/__pycache__/llm.cpython-313.pyc,,
aipyapp/config/base.py,sha256=grfC0GYOwCO9IXrYdprlHV1LAK04wMVCau4DPhgnVvc,650
aipyapp/config/llm.py,sha256=WhoyTUgQ5pEPRKYiT9sNvI3whUfWsZMEB82Kpp5XniY,1715
aipyapp/exec/__init__.py,sha256=PplILm0nFi6RYrDlJQAfjsrNnEWqN3j_0gRv5Xm5j3g,116
aipyapp/exec/__pycache__/__init__.cpython-313.pyc,,
aipyapp/exec/__pycache__/executor.cpython-313.pyc,,
aipyapp/exec/__pycache__/html.cpython-313.pyc,,
aipyapp/exec/__pycache__/prun.cpython-313.pyc,,
aipyapp/exec/executor.py,sha256=_ImmoXQI1ypEn7mvTvgXrkeRwLjrSls35kMwdlQFewY,2279
aipyapp/exec/html.py,sha256=YYeIby3UG_f9cAd3FItclMcHJd3OVtgtLeabmn-23LA,461
aipyapp/exec/prun.py,sha256=FVW2q8LtzMW8RZL6GmjydrOjaXuur-pJRbfWxwFNU90,2179
aipyapp/exec/python/__init__.py,sha256=WRr520EpRu2onUcqOpaTnNxCAouclnQM5SwbSxRVFiM,119
aipyapp/exec/python/__pycache__/__init__.cpython-313.pyc,,
aipyapp/exec/python/__pycache__/executor.cpython-313.pyc,,
aipyapp/exec/python/__pycache__/mod_dict.cpython-313.pyc,,
aipyapp/exec/python/__pycache__/mod_obj.cpython-313.pyc,,
aipyapp/exec/python/__pycache__/runtime.cpython-313.pyc,,
aipyapp/exec/python/executor.py,sha256=RzsQBXWF_F62v4MJuYABDouVqrp-QQZtGaqV-rnGAKY,3288
aipyapp/exec/python/mod_dict.py,sha256=wXuXnboyWBQDne8l8HEpBracUJnUH5TT-2Cfdyv3MkY,2725
aipyapp/exec/python/mod_obj.py,sha256=PfK_FflY9Aowv3w7X02Lm5GZ7hd-kWhLubdZ0y0xfdE,2207
aipyapp/exec/python/runtime.py,sha256=CkRjxSBvMZDIBbV-WoRP4fcwsxs092CA_2M_60hPjI4,2822
aipyapp/gui/__init__.py,sha256=f4oV4KKnknrbL5kAx8kvJVez51RcXOpzsXdznejFbXQ,349
aipyapp/gui/__pycache__/__init__.cpython-313.pyc,,
aipyapp/gui/__pycache__/about.cpython-313.pyc,,
aipyapp/gui/__pycache__/apimarket.cpython-313.pyc,,
aipyapp/gui/__pycache__/config.cpython-313.pyc,,
aipyapp/gui/__pycache__/main.cpython-313.pyc,,
aipyapp/gui/__pycache__/providers.cpython-313.pyc,,
aipyapp/gui/__pycache__/statusbar.cpython-313.pyc,,
aipyapp/gui/__pycache__/trustoken.cpython-313.pyc,,
aipyapp/gui/about.py,sha256=q5cbZRTuT_cg4uT5R7Txx0gMrejbrFOI1codZVDXmTo,2502
aipyapp/gui/apimarket.py,sha256=f2a6QFvBg_tvkd9T7mWisOdk6iZp8VejS9PqQaOs8Ck,23927
aipyapp/gui/config.py,sha256=Sljw91aGZop35SgVxL0dqkMYUHtM6QOotoTKaFDrDx0,8366
aipyapp/gui/main.py,sha256=GStXbPkp2m3edG2v0ahNP49KvTK8HxaaQHqNFLSiUek,21738
aipyapp/gui/providers.py,sha256=FZlamE5wgEm-q4KZkF0x-wRf3QFplnFq_tnufi_H68w,20715
aipyapp/gui/statusbar.py,sha256=M_IYLjMuH8omdvHeKf4ac570hIhKI-epuif4UAAyEGg,2653
aipyapp/gui/trustoken.py,sha256=NSNtDkQuAl2dWAh56wvY77WeCZpdM0N8cI2V8plNGlk,8040
aipyapp/i18n.py,sha256=fK33A_rMotxeyFKeiQr8GL0UtbsDGq5uzXtyI0O0-u0,4133
aipyapp/llm/__init__.py,sha256=2qikd_hxFGnoHv9gIvI4x1eZmByrcHn8Fp4jDNHQ_is,1921
aipyapp/llm/__pycache__/__init__.cpython-313.pyc,,
aipyapp/llm/__pycache__/base.cpython-313.pyc,,
aipyapp/llm/__pycache__/base_openai.cpython-313.pyc,,
aipyapp/llm/__pycache__/client_claude.cpython-313.pyc,,
aipyapp/llm/__pycache__/client_ollama.cpython-313.pyc,,
aipyapp/llm/__pycache__/models.cpython-313.pyc,,
aipyapp/llm/base.py,sha256=V79YFF0Fba2R9inMiGOuhFRvU0DjlsDTp5TAI2ekeXQ,3257
aipyapp/llm/base_openai.py,sha256=VqsnoNkXDfXUwW392KaeapPYwbL68l2-K5vundtBbx0,2937
aipyapp/llm/client_claude.py,sha256=0h2OlNpHzh_gUvAgs57l3JoMU8lzuu5FWoZMEJ2cFB4,2643
aipyapp/llm/client_ollama.py,sha256=R2Y7CYjVmptceAjtLroL3IOYCv_3XkpLq5iBJRGyg8Y,1946
aipyapp/llm/models.py,sha256=A-Bsve9QVe9eoFSz_hFuEZpPHJfBJlqatcolyUl4bbI,4543
aipyapp/res/DISCLAIMER.md,sha256=IKGdJR3zxZcDnrfVxd7MDc9pm_QEhmAMXxM1a-tDFy0,834
aipyapp/res/aipy.ico,sha256=PyTGMt-2_Xo7oCRHZzvgl_AZCCKtr8ynCCEVZRXvpaY,134982
aipyapp/res/chatroom_en.html,sha256=85O8QDwlnowZIjSUbTV7itVk86sOeVC8qypiQHsi3CY,14571
aipyapp/res/chatroom_zh.html,sha256=SSx5UdRL24I8ginkw9jCCRB2erxkBFOUUXail1IPoP4,14593
aipyapp/res/console_code.html,sha256=cwa1juatDdD6zLOW74gv-VJ8SAVMdZoAWI8Pnh5d1-s,9632
aipyapp/res/console_white.html,sha256=zG7cemVFwNgTHm5bsqALZRwWP6qESSDKdnLaq9nJkHs,1916
aipyapp/res/css/github-dark.min.css,sha256=nyCNAiECsdDHrr_s2OQsp5l9XeY2ZJ0rMepjCT2AkBk,1315
aipyapp/res/css/github.min.css,sha256=Oppd74ucMR5a5Dq96FxjEzGF7tTw2fZ_6ksAqDCM8GY,1309
aipyapp/res/default.toml,sha256=VdnfNPcD6wU6pVBKqRAol54jz4cACycwdYq2MFkV-p8,250
aipyapp/res/js/highlight.min.js,sha256=nxnOvB1EQa4fD_xpakJii5-GX-XZne2h8ejNW-yHiIg,120762
aipyapp/res/js/marked.min.js,sha256=xoB1Zy2Xbkd3OQVguqESGUhVvUQEsTZH2khVquH5Ngw,49718
aipyapp/res/js/purify.min.js,sha256=6ksJCCykugrnG-ZDGgl2eHUdBFO5xSpNLHw5ohZu2fw,20931
aipyapp/res/locales.csv,sha256=7EJFx2vAbhdvnCvRKlg80EcgtwXprhYEuJ0hYaP-f7M,30039
aipyapp/res/models.yaml,sha256=NqC4nwnpAw78vB-UZyan95wnng-Tzf7ASspnsWMtJtI,6028
aipyapp/res/tips/aipy.toml,sha256=Ta_VgJHmxsAqvKt2jL8fV2A1uCI4I6ydAIZ26mpPZkk,3120
aipyapp/runtime.py,sha256=fzWb2CsTIj89OjzW1bDFM-zsmY-cArUYnPDes0NZYrA,676
