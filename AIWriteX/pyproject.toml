[project]
name = "ai_write_x"
version = "1.0.0"
description = "ai_write_x using crewAI"
authors = [{ name = "iniwap", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = ["crewai[tools]>=0.102.0,<1.0.0"]

[project.scripts]
ai_write_x = "ai_write_x.main:run"
run_crew = "ai_write_x.main:run"
train = "ai_write_x.main:train"
replay = "ai_write_x.main:replay"
test = "ai_write_x.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
