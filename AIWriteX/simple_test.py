#!/usr/bin/env python3
"""
简化的AIWriteX测试 - 直接测试API调用
"""

import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_openrouter_api():
    """直接测试OpenRouter API调用"""
    print("🚀 测试OpenRouter API直接调用")
    print("=" * 50)
    
    # 使用最佳配置
    api_key = "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"
    model = "meta-llama/llama-3.2-3b-instruct:free"
    
    try:
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://aiwritex.com",
                "X-Title": "AIWriteX Simple Test"
            },
            json={
                "model": model,
                "messages": [
                    {"role": "system", "content": "你是一个专业的文章写作助手，擅长创作高质量的中文文章。"},
                    {"role": "user", "content": "请写一篇关于'人工智能在教育领域的应用'的文章，要求：1. 字数在800-1000字之间 2. 包含引言、主体和结论 3. 语言流畅，逻辑清晰"}
                ],
                "max_tokens": 1500,
                "temperature": 0.7
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'choices' in data and len(data['choices']) > 0:
                content = data['choices'][0]['message']['content']
                
                print("✅ API调用成功！")
                print(f"📝 生成文章长度: {len(content)}字符")
                print(f"🤖 使用模型: {model}")
                print(f"⏱️  响应时间: 正常")
                
                # 保存生成的文章
                output_dir = "output/test"
                os.makedirs(output_dir, exist_ok=True)
                
                with open(f"{output_dir}/test_article.txt", "w", encoding="utf-8") as f:
                    f.write(content)
                
                print(f"💾 文章已保存到: {output_dir}/test_article.txt")
                
                # 显示文章预览
                print("\n📖 文章预览:")
                print("-" * 50)
                print(content[:300] + "..." if len(content) > 300 else content)
                print("-" * 50)
                
                return True, content
            else:
                print("❌ API响应格式异常")
                return False, None
        else:
            print(f"❌ API调用失败 - 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ API调用出错: {e}")
        return False, None

def test_article_generation():
    """测试文章生成功能"""
    print("\n🎯 测试文章生成功能")
    print("=" * 50)
    
    topics = [
        "科技创新与未来发展",
        "健康生活方式的重要性", 
        "环保与可持续发展",
        "数字化时代的机遇与挑战"
    ]
    
    success_count = 0
    
    for i, topic in enumerate(topics, 1):
        print(f"\n📝 测试主题 {i}: {topic}")
        
        success, article = test_openrouter_api_with_topic(topic)
        if success:
            success_count += 1
            print(f"✅ 主题 {i} 生成成功")
        else:
            print(f"❌ 主题 {i} 生成失败")
        
        # 避免请求过快
        time.sleep(2)
    
    print(f"\n📊 测试结果: {success_count}/{len(topics)} 成功")
    return success_count == len(topics)

def test_openrouter_api_with_topic(topic):
    """使用指定主题测试API"""
    api_key = "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"
    model = "meta-llama/llama-3.2-3b-instruct:free"
    
    try:
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://aiwritex.com",
                "X-Title": "AIWriteX Topic Test"
            },
            json={
                "model": model,
                "messages": [
                    {"role": "system", "content": "你是一个专业的文章写作助手，擅长创作高质量的中文文章。"},
                    {"role": "user", "content": f"请写一篇关于'{topic}'的文章，要求：1. 字数在500-800字之间 2. 包含引言、主体和结论 3. 语言流畅，逻辑清晰"}
                ],
                "max_tokens": 1200,
                "temperature": 0.7
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'choices' in data and len(data['choices']) > 0:
                content = data['choices'][0]['message']['content']
                return True, content
        
        return False, None
        
    except Exception as e:
        print(f"   错误: {e}")
        return False, None

def main():
    """主函数"""
    print("🎉 AIWriteX 简化测试")
    print("=" * 50)
    
    # 基础API测试
    print("1️⃣ 基础API测试")
    basic_success, _ = test_openrouter_api()
    
    if basic_success:
        print("\n2️⃣ 多主题测试")
        multi_success = test_article_generation()
        
        if multi_success:
            print("\n🎉 所有测试通过！")
            print("✅ AIWriteX核心功能正常")
            print("✅ API配置完美")
            print("✅ 文章生成质量良好")
            
            print("\n🚀 下一步建议:")
            print("1. 等待微信IP白名单生效")
            print("2. 尝试完整的AIWriteX程序")
            print("3. 开始正式的文章创作")
        else:
            print("\n⚠️  部分测试失败，但基础功能正常")
    else:
        print("\n❌ 基础测试失败，请检查配置")

if __name__ == "__main__":
    main()
