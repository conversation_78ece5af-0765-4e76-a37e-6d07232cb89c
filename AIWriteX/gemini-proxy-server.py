#!/usr/bin/env python3
"""
本地Gemini代理服务器 - 解决国内访问问题
"""

from flask import Flask, request, jsonify
import requests
import json
import time
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Gemini API Keys
GEMINI_KEYS = [
    "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ",
    "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
]

# 访问令牌
ALLOWED_TOKENS = ["sk-gemini-proxy-1", "sk-gemini-proxy-2", "sk-gemini-proxy-admin"]

key_usage_stats = {}

def get_next_api_key():
    """轮换API Key"""
    now = time.time()
    best_key = GEMINI_KEYS[0]
    min_usage = float('inf')
    
    for key in GEMINI_KEYS:
        usage = key_usage_stats.get(key, {'count': 0, 'last_used': 0})
        if now - usage['last_used'] > 3600:
            usage['count'] = 0
        if usage['count'] < min_usage:
            min_usage = usage['count']
            best_key = key
    
    if best_key not in key_usage_stats:
        key_usage_stats[best_key] = {'count': 0, 'last_used': 0}
    
    key_usage_stats[best_key]['count'] += 1
    key_usage_stats[best_key]['last_used'] = now
    return best_key

def validate_token(auth_header):
    """验证令牌"""
    if not auth_header or not auth_header.startswith('Bearer '):
        return False
    token = auth_header[7:]
    return token in ALLOWED_TOKENS

@app.route('/v1/models', methods=['GET'])
def list_models():
    """模型列表"""
    if not validate_token(request.headers.get('Authorization')):
        return jsonify({'error': 'Unauthorized'}), 401
    
    return jsonify({
        'object': 'list',
        'data': [
            {'id': 'gemini-1.5-flash', 'object': 'model', 'owned_by': 'google'},
            {'id': 'gemini-1.5-pro', 'object': 'model', 'owned_by': 'google'},
            {'id': 'gemini-2.0-flash', 'object': 'model', 'owned_by': 'google'}
        ]
    })

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """聊天完成"""
    if not validate_token(request.headers.get('Authorization')):
        return jsonify({'error': 'Unauthorized'}), 401
    
    try:
        data = request.json
        messages = data.get('messages', [])
        model = data.get('model', 'gemini-1.5-flash')
        
        # 转换为Gemini格式
        contents = []
        for msg in messages:
            if msg['role'] == 'user':
                contents.append({'parts': [{'text': msg['content']}]})
        
        api_key = get_next_api_key()
        
        # 调用Gemini API
        response = requests.post(
            f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}",
            headers={'Content-Type': 'application/json'},
            json={'contents': contents},
            timeout=30
        )
        
        if response.status_code == 200:
            gemini_data = response.json()
            if gemini_data.get('candidates'):
                content = gemini_data['candidates'][0]['content']['parts'][0]['text']
                return jsonify({
                    'id': f'chatcmpl-{int(time.time())}',
                    'object': 'chat.completion',
                    'created': int(time.time()),
                    'model': model,
                    'choices': [{
                        'index': 0,
                        'message': {'role': 'assistant', 'content': content},
                        'finish_reason': 'stop'
                    }]
                })
        
        return jsonify({'error': 'Gemini API error'}), 500
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/status')
def status():
    """状态页面"""
    return jsonify({
        'status': 'running',
        'api_keys_count': len(GEMINI_KEYS),
        'usage_stats': key_usage_stats
    })

@app.route('/')
def home():
    """首页"""
    return """
    <h1>🚀 Gemini Proxy Server</h1>
    <p>本地Gemini API代理服务</p>
    <ul>
        <li>API端点: http://localhost:8080/v1</li>
        <li>状态: <a href="/status">http://localhost:8080/status</a></li>
        <li>令牌: sk-gemini-proxy-1, sk-gemini-proxy-2, sk-gemini-proxy-admin</li>
    </ul>
    """

if __name__ == '__main__':
    print("🚀 启动Gemini代理服务器...")
    print("📡 地址: http://localhost:8080")
    print("🔑 令牌: sk-gemini-proxy-1")
    app.run(host='0.0.0.0', port=8080, debug=False)
