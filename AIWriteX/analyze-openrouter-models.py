#!/usr/bin/env python3
"""
分析OpenRouter可用的免费模型
"""

import requests
import json

def get_free_models():
    """获取所有免费模型"""
    print("📋 获取OpenRouter模型列表...")
    
    try:
        response = requests.get("https://openrouter.ai/api/v1/models", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('data', [])
            
            print(f"✅ 获取到 {len(models)} 个模型")
            
            # 筛选免费模型
            free_models = []
            for model in models:
                pricing = model.get('pricing', {})
                prompt_price = pricing.get('prompt', '0')
                completion_price = pricing.get('completion', '0')
                
                # 检查是否为免费模型
                if (prompt_price == '0' and completion_price == '0') or ':free' in model['id']:
                    free_models.append({
                        'id': model['id'],
                        'name': model['name'],
                        'context_length': model.get('context_length', 0),
                        'description': model.get('description', '')[:100] + '...',
                        'input_modalities': model.get('architecture', {}).get('input_modalities', []),
                        'supported_parameters': model.get('supported_parameters', [])
                    })
            
            return free_models
        else:
            print(f"❌ 获取模型列表失败 - 状态码: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 获取模型列表错误: {e}")
        return []

def categorize_models(models):
    """按类型分类模型"""
    categories = {
        'chat': [],
        'code': [],
        'reasoning': [],
        'multimodal': [],
        'other': []
    }
    
    for model in models:
        model_id = model['id'].lower()
        description = model['description'].lower()
        
        # 分类逻辑
        if any(keyword in model_id for keyword in ['llama', 'gemma', 'phi', 'qwen']):
            categories['chat'].append(model)
        elif any(keyword in model_id for keyword in ['code', 'deepseek']):
            categories['code'].append(model)
        elif any(keyword in description for keyword in ['reasoning', 'thinking']):
            categories['reasoning'].append(model)
        elif 'image' in model.get('input_modalities', []):
            categories['multimodal'].append(model)
        else:
            categories['other'].append(model)
    
    return categories

def recommend_models(models):
    """推荐最适合的模型"""
    recommendations = {
        'best_overall': None,
        'best_for_writing': None,
        'best_for_code': None,
        'largest_context': None
    }
    
    # 按上下文长度排序
    models_by_context = sorted(models, key=lambda x: x['context_length'], reverse=True)
    
    # 推荐逻辑
    for model in models:
        model_id = model['id'].lower()
        
        # 最佳整体模型
        if 'llama-3.2' in model_id and ':free' in model['id']:
            recommendations['best_overall'] = model
        
        # 最佳写作模型
        if 'gemma' in model_id and ':free' in model['id']:
            recommendations['best_for_writing'] = model
        
        # 最佳编程模型
        if 'deepseek' in model_id or 'code' in model_id:
            recommendations['best_for_code'] = model
    
    # 最大上下文
    if models_by_context:
        recommendations['largest_context'] = models_by_context[0]
    
    return recommendations

def main():
    """主函数"""
    print("🚀 OpenRouter 免费模型分析工具")
    print("=" * 60)
    
    # 获取免费模型
    free_models = get_free_models()
    
    if not free_models:
        print("❌ 未找到免费模型")
        return
    
    print(f"\n🎉 找到 {len(free_models)} 个免费模型")
    
    # 分类模型
    categories = categorize_models(free_models)
    
    print("\n📊 模型分类:")
    for category, models in categories.items():
        if models:
            print(f"\n🔸 {category.upper()} ({len(models)}个):")
            for model in models[:3]:  # 只显示前3个
                print(f"   • {model['id']}")
                print(f"     上下文: {model['context_length']:,} tokens")
    
    # 推荐模型
    recommendations = recommend_models(free_models)
    
    print("\n🏆 推荐模型:")
    print("=" * 40)
    
    for purpose, model in recommendations.items():
        if model:
            print(f"\n🎯 {purpose.replace('_', ' ').title()}:")
            print(f"   模型: {model['id']}")
            print(f"   名称: {model['name']}")
            print(f"   上下文: {model['context_length']:,} tokens")
            print(f"   支持功能: {', '.join(model['supported_parameters'][:3])}...")
    
    # 生成AIWriteX配置
    print("\n🔧 AIWriteX 配置建议:")
    print("=" * 40)
    
    best_models = [model for model in recommendations.values() if model]
    if best_models:
        print("\n推荐模型列表（按优先级）:")
        for i, model in enumerate(best_models[:5], 1):
            print(f"   {i}. {model['id']}")
        
        print(f"\n配置示例:")
        print(f"```yaml")
        print(f"api:")
        print(f"  api_type: OpenRouter")
        print(f"  OpenRouter:")
        print(f"    model_index: 0")
        print(f"    model:")
        for model in best_models[:3]:
            print(f"      - {model['id']}")
        print(f"```")
    
    # 显示所有免费模型
    print(f"\n📋 所有免费模型列表:")
    print("=" * 40)
    for i, model in enumerate(free_models, 1):
        print(f"{i:2d}. {model['id']}")
        if model['context_length'] > 0:
            print(f"     上下文: {model['context_length']:,} tokens")

if __name__ == "__main__":
    main()
