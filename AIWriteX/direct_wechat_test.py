#!/usr/bin/env python3
"""
直接使用AIWriteX项目代码测试微信配置
"""

import sys
import os
import requests
import json
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_wechat_with_project_code():
    """使用项目实际代码测试微信配置"""
    print("🔧 使用AIWriteX项目代码测试微信配置...")
    
    # 微信配置（从config.yaml中读取的实际配置）
    app_id = "wx9538fd0990debc8f"
    app_secret = "b08b41349d87b3ad6d34340436e386e1"
    author = "ljli"
    
    print(f"AppID: {app_id}")
    print(f"AppSecret: {app_secret[:10]}...")
    print(f"Author: {author}")
    
    # 使用项目中的实际代码逻辑
    BASE_URL = "https://api.weixin.qq.com/cgi-bin"
    
    try:
        # 完全按照项目代码的方式调用
        url = f"{BASE_URL}/token?grant_type=client_credential&appid={app_id}&secret={app_secret}"
        print(f"\n🌐 请求URL: {url}")
        
        response = requests.get(url, timeout=30)
        print(f"📡 HTTP状态码: {response.status_code}")
        
        response.raise_for_status()
        data = response.json()
        
        print(f"📄 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        access_token = data.get("access_token")
        expires_in = data.get("expires_in")
        
        if not access_token:
            print(f"❌ 获取access_token失败: {data}")
            return False
        
        print("✅ 微信配置测试成功！")
        print(f"   Access Token: {access_token[:20]}...")
        print(f"   过期时间: {expires_in}秒")
        
        # 计算过期时间
        expires_at = datetime.now() + timedelta(seconds=expires_in)
        print(f"   过期时刻: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 测试账号认证状态
        print("\n🔍 测试账号认证状态...")
        verify_url = f"{BASE_URL}/account/getaccountbasicinfo?access_token={access_token}"
        verify_response = requests.get(verify_url, timeout=10)
        
        if verify_response.status_code == 200:
            verify_data = verify_response.json()
            print(f"📄 账号信息: {json.dumps(verify_data, indent=2, ensure_ascii=False)}")
            
            wx_verify = verify_data.get("wx_verify_info", {})
            is_verified = bool(wx_verify.get("qualification_verify", False))
            print(f"🏆 账号认证状态: {'已认证' if is_verified else '未认证'}")
        else:
            print(f"⚠️  无法获取账号认证状态: {verify_response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        print(f"原始响应: {response.text}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 AIWriteX 微信配置直接测试")
    print("=" * 60)
    
    success = test_wechat_with_project_code()
    
    print("\n" + "=" * 60)
    print("📋 测试结果")
    print("=" * 60)
    
    if success:
        print("🎉 微信配置完全正常！")
        print("✅ 可以获取access_token")
        print("✅ IP白名单配置正确")
        print("✅ AppID和AppSecret有效")
        print("\n🚀 现在可以启动AIWriteX进行文章生成和发布了！")
    else:
        print("❌ 微信配置存在问题")
        print("🔧 请检查：")
        print("   1. IP白名单是否包含当前IP")
        print("   2. AppID和AppSecret是否正确")
        print("   3. 网络连接是否正常")

if __name__ == "__main__":
    main()
