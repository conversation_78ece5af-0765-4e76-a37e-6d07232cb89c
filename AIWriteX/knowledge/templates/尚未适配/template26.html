<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星辰大学周报</title>
</head>
<body>
    <section style="padding: 20px 16px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif; color: #333; line-height: 1.6; margin: 0; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-size: 16px;">
        <div style="text-align: center; margin-bottom: 24px;">
            <svg width="120" height="60" viewBox="0 0 120 60">
                <defs>
                    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#4361ee" />
                        <stop offset="100%" style="stop-color:#7209b7" />
                    </linearGradient>
                </defs>
                <path d="M20,10 L100,10 L100,50 L20,50 Z" fill="none" stroke="url(#logoGradient)" stroke-width="3">
                    <animate attributeName="stroke-dasharray" from="0,360" to="360,0" dur="2s" repeatCount="indefinite" />
                </path>
                <text x="60" y="36" font-family="Arial" font-size="14" fill="#333" text-anchor="middle" font-weight="bold">星辰大学周报</text>
            </svg>
        </div>
        
        <div style="font-size: 28px; font-weight: 700; margin-bottom: 8px; color: #4361ee; letter-spacing: 1px; text-align: center;">星辰大学周报</div>
        <div style="font-size: 16px; color: #7209b7; margin-bottom: 24px; text-align: center; font-weight: 500;">第47期 | 2025年6月第3周</div>
    </section>

    <section style="padding: 24px 18px; background-color: white; border-radius: 16px; box-shadow: 0 6px 16px rgba(0,0,0,0.08); margin: 0 16px 24px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;">
        <div style="font-size: 22px; font-weight: 700; margin-bottom: 16px; color: #4361ee; letter-spacing: 0.5px; border-left: 4px solid #7209b7; padding-left: 12px;">本周焦点</div>
        
        <div style="font-size: 19px; font-weight: 600; margin: 20px 0 12px; color: #333;">星辰大学举办首届"未来科技创新论坛"</div>
        <div style="font-size: 15px; line-height: 1.7; color: #444; margin-bottom: 16px; letter-spacing: 0.3px;">
            本周二，星辰大学成功举办了首届"未来科技创新论坛"，来自全国各地的科技领军人物、学者及500余名师生参与。论坛聚焦人工智能、量子计算及绿色能源三大主题，校长李明远在开幕式上强调"跨学科创新"对未来科技发展的重要性。本次论坛还特别邀请了三位海外华人科学家进行远程分享，他们对星辰大学近年来在科研领域的突破给予高度评价。
        </div>
        
        <div style="margin: 24px 0; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
            <img src="https://picsum.photos/600/300?random=1" alt="未来科技创新论坛现场" style="width: 100%; height: auto; display: block; border-radius: 12px;">
            <div style="padding: 12px; font-size: 14px; color: #666; text-align: center; background-color: #f8f9fa;">未来科技创新论坛现场</div>
        </div>
    </section>

    <section style="padding: 24px 18px; background-color: white; border-radius: 16px; box-shadow: 0 6px 16px rgba(0,0,0,0.08); margin: 0 16px 24px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;">
        <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <svg width="24" height="24" viewBox="0 0 24 24" style="margin-right: 8px; fill: #7209b7;">
                <path d="M3,3v18h18V3H3z M19,19H5V5h14V19z M7,7h10v2H7V7z M7,11h10v2H7V11z M7,15h7v2H7V15z"></path>
            </svg>
            <div style="font-size: 20px; font-weight: 700; color: #4361ee; letter-spacing: 0.5px;">教学科研</div>
        </div>
        
        <div style="padding: 16px; background: linear-gradient(to right, rgba(67, 97, 238, 0.1), rgba(114, 9, 183, 0.05)); border-radius: 12px; margin-bottom: 20px;">
            <div style="font-size: 17px; font-weight: 600; margin-bottom: 8px; color: #333;">计算机学院发布新一代操作系统研究成果</div>
            <div style="font-size: 15px; line-height: 1.7; color: #444;">
                计算机学院张教授团队历时三年研发的"星辰OS"正式发布首个测试版。该操作系统针对高性能计算场景优化，在大规模数据处理上较传统系统提速40%。科技部已将其列入国家重点科技项目，预计明年实现商业化应用。该成果标志着我校在操作系统自主研发领域取得重大突破。
            </div>
        </div>
        
        <div style="padding: 16px; background: linear-gradient(to right, rgba(114, 9, 183, 0.05), rgba(67, 97, 238, 0.1)); border-radius: 12px;">
            <div style="font-size: 17px; font-weight: 600; margin-bottom: 8px; color: #333;">医学院与市第一医院签署战略合作协议</div>
            <div style="font-size: 15px; line-height: 1.7; color: #444;">
                周四上午，我校医学院与市第一医院正式签署为期十年的战略合作协议。双方将共建"医学转化研究中心"，促进医学前沿研究成果快速转化为临床应用。协议还包括联合培养医学人才计划，每年将为医学院优秀学生提供30个临床实习名额，助力培养高素质医学人才。
            </div>
        </div>
    </section>

    <section style="padding: 24px 18px; background-color: white; border-radius: 16px; box-shadow: 0 6px 16px rgba(0,0,0,0.08); margin: 0 16px 24px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <div style="font-size: 20px; font-weight: 700; color: #4361ee; letter-spacing: 0.5px;">校园生活</div>
            <div style="width: 40px; height: 4px; background: linear-gradient(to right, #4361ee, #7209b7); border-radius: 2px;"></div>
        </div>

        <div style="display: flex; margin-bottom: 20px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.07);">
            <div style="flex: 0 0 100px;">
                <img src="https://picsum.photos/200/200?random=2" alt="艺术节表演" style="width: 100px; height: 100px; object-fit: cover;">
            </div>
            <div style="flex: 1; padding: 12px 16px; background-color: #f8f9fa;">
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 6px; color: #333;">第十八届校园艺术节圆满落幕</div>
                <div style="font-size: 14px; line-height: 1.5; color: #555;">为期两周的校园艺术节于周日闭幕，共举办24场活动，参与学生超过3000人。本届艺术节以"青春·融合·创新"为主题，呈现出多元文化交融的艺术盛宴。</div>
            </div>
        </div>

        <div style="display: flex; margin-bottom: 20px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.07);">
            <div style="flex: 0 0 100px;">
                <img src="https://picsum.photos/200/200?random=3" alt="志愿服务" style="width: 100px; height: 100px; object-fit: cover;">
            </div>
            <div style="flex: 1; padding: 12px 16px; background-color: #f8f9fa;">
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 6px; color: #333;">青年志愿者走进社区开展环保宣传</div>
                <div style="font-size: 14px; line-height: 1.5; color: #555;">本周六，来自环境科学学院的50余名志愿者走进周边社区，通过趣味互动游戏、环保知识讲座等形式，向居民普及垃圾分类和节能减排知识。</div>
            </div>
        </div>

        <div style="display: flex; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.07);">
            <div style="flex: 0 0 100px;">
                <img src="https://picsum.photos/200/200?random=4" alt="体育赛事" style="width: 100px; height: 100px; object-fit: cover;">
            </div>
            <div style="flex: 1; padding: 12px 16px; background-color: #f8f9fa;">
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 6px; color: #333;">校足球队勇夺省大学生联赛冠军</div>
                <div style="font-size: 14px; line-height: 1.5; color: #555;">我校足球队在周三结束的省大学生足球联赛决赛中，以3:1击败省理工大学，时隔五年再次捧起冠军奖杯。这也是我校足球队历史上第四次获得此项赛事冠军。</div>
            </div>
        </div>
    </section>

    <section style="padding: 24px 18px; background-color: white; border-radius: 16px; box-shadow: 0 6px 16px rgba(0,0,0,0.08); margin: 0 16px 24px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;">
        <div style="font-size: 20px; font-weight: 700; margin-bottom: 20px; color: #4361ee; text-align: center; letter-spacing: 0.5px;">
            学术成果 & 奖项荣誉
        </div>

        <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
            <div style="width: 48%; padding: 16px; background-color: rgba(67, 97, 238, 0.08); border-radius: 12px; text-align: center;">
                <svg width="40" height="40" viewBox="0 0 24 24" style="margin: 0 auto 8px; fill: #4361ee;">
                    <path d="M12,2L4,5v6.09c0,5.05,3.41,9.76,8,10.91c4.59-1.15,8-5.86,8-10.91V5L12,2z M18,11.09c0,4-2.55,7.7-6,8.83 c-3.45-1.13-6-4.82-6-8.83v-4.7l6-2.25l6,2.25V11.09z M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5S14.76,7,12,7z M12,15 c-1.66,0-3-1.34-3-3s1.34-3,3-3s3,1.34,3,3S13.66,15,12,15z"></path>
                </svg>
                <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px; color: #333;">5篇</div>
                <div style="font-size: 14px; color: #666;">Nature/Science论文</div>
            </div>
            <div style="width: 48%; padding: 16px; background-color: rgba(114, 9, 183, 0.08); border-radius: 12px; text-align: center;">
                <svg width="40" height="40" viewBox="0 0 24 24" style="margin: 0 auto 8px; fill: #7209b7;">
                    <path d="M12,3L1,9l4,2.18v6L12,21l7-3.82v-6l2-1.09V17h2V9L12,3z M18.82,9L12,12.72L5.18,9L12,5.28L18.82,9z M17,15.99l-5,2.73 l-5-2.73v-3.72L12,15l5-2.73V15.99z"></path>
                </svg>
                <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px; color: #333;">42个</div>
                <div style="font-size: 14px; color: #666;">国家级科研项目</div>
            </div>
        </div>

        <div style="margin-top: 24px;">
            <div style="font-size: 16px; font-weight: 600; margin-bottom: 12px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 8px;">本周获奖名单</div>
            <div style="font-size: 15px; line-height: 1.8; color: #444;">
                <div style="margin-bottom: 10px;">• 化学系王教授团队获国家自然科学二等奖</div>
                <div style="margin-bottom: 10px;">• 机械工程学院学生创新团队在全国大学生机器人大赛中获特等奖</div>
                <div style="margin-bottom: 10px;">• 外国语学院张教授主编的《多语言翻译理论与实践》获评国家精品教材</div>
                <div style="margin-bottom: 10px;">• 经济管理学院博士生李明在国际经济学年会上获最佳论文奖</div>
            </div>
        </div>
    </section>

    <section style="padding: 24px 18px; background-color: white; border-radius: 16px; box-shadow: 0 6px 16px rgba(0,0,0,0.08); margin: 0 16px 24px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;">
        <div style="display: flex; align-items: center; margin-bottom: 20px;">
            <div style="width: 4px; height: 24px; background: linear-gradient(to bottom, #4361ee, #7209b7); margin-right: 12px; border-radius: 2px;"></div>
            <div style="font-size: 20px; font-weight: 700; color: #4361ee;">国际交流</div>
        </div>

        <div style="margin-bottom: 20px; padding: 16px; background-color: #f8f9fa; border-radius: 12px;">
            <div style="font-size: 17px; font-weight: 600; margin-bottom: 10px; color: #333;">我校与德国慕尼黑工业大学签署学生交换协议</div>
            <div style="font-size: 15px; line-height: 1.7; color: #555;">
                周三，校长李明远与来访的德国慕尼黑工业大学代表团签署了为期五年的学生交换协议。根据协议，双方每学年将互派20名优秀学生进行为期一学期的交换学习。首批交换生将于今年9月出发。此次合作是我校国际化战略的重要一步，将为学生提供宝贵的国际学习经验。
            </div>
        </div>

        <div style="padding: 16px; background-color: #f8f9fa; border-radius: 12px;">
            <div style="font-size: 17px; font-weight: 600; margin-bottom: 10px; color: #333;">国际学术周活动圆满结束</div>
            <div style="font-size: 15px; line-height: 1.7; color: #555;">
                为期一周的国际学术周于周五闭幕。本次活动邀请了来自12个国家的28位知名学者，举办了18场专题讲座，涵盖人工智能、生物医药、气候变化等前沿领域。学术周期间，还举办了4场国际合作项目洽谈会，初步达成7项合作意向。这是我校迄今为止规模最大的国际学术交流活动。
            </div>
        </div>
    </section>

    <section style="padding: 24px 18px; background-color: white; border-radius: 16px; box-shadow: 0 6px 16px rgba(0,0,0,0.08); margin: 0 16px 24px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 20px;">
            <div style="display: inline-block; padding: 8px 20px; background: linear-gradient(45deg, #4361ee, #7209b7); color: white; border-radius: 30px; font-weight: 600; font-size: 16px;">下周预告</div>
        </div>

        <div style="margin-bottom: 16px; padding: 16px; border-left: 3px solid #4361ee; background-color: #f8f9fa;">
            <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px; color: #333;">6月23日 周一</div>
            <div style="font-size: 15px; line-height: 1.6; color: #555;">
                • 09:00 校园开放日活动（中心广场）<br>
                • 14:30 "职业规划与就业指导"讲座（图书馆报告厅）<br>
                • 19:00 星辰大讲堂：诺贝尔物理学奖得主专题报告会（大礼堂）
            </div>
        </div>

        <div style="margin-bottom: 16px; padding: 16px; border-left: 3px solid #7209b7; background-color: #f8f9fa;">
            <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px; color: #333;">6月25日 周三</div>
            <div style="font-size: 15px; line-height: 1.6; color: #555;">
                • 10:00 2025届毕业生就业双选会（体育馆）<br>
                • 15:00 校友创业经验分享会（经管学院报告厅）<br>
                • 19:30 校园电影之夜（露天剧场）
            </div>
        </div>

        <div style="padding: 16px; border-left: 3px solid #4361ee; background-color: #f8f9fa;">
            <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px; color: #333;">6月27-28日 周五至周六</div>
            <div style="font-size: 15px; line-height: 1.6; color: #555;">
                • 全天 "人工智能与未来教育"国际研讨会（学术交流中心）<br>
                • 全天 校园科技创新成果展（科技楼广场）<br>
                • 19:00 周六 校园交响乐之夜（音乐厅）
            </div>
        </div>
    </section>

    <section style="padding: 24px 18px; background: linear-gradient(135deg, #4361ee 0%, #7209b7 100%); border-radius: 16px; margin: 0 16px 24px; font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif; text-align: center; color: white;">
        <div style="font-size: 18px; font-weight: 600; margin-bottom: 12px;">星辰大学校训</div>
        <div style="font-size: 22px; font-weight: 700; margin-bottom: 16px; letter-spacing: 2px;">"求真 创新 担当"</div>
        <div style="font-size: 14px; line-height: 1.7; max-width: 280px; margin: 0 auto;">
            携手共进，追求卓越<br>
            星辰大学与您共同成长
        </div>

        <svg width="100" height="40" viewBox="0 0 100 40" style="margin: 16px auto 0;">
            <path d="M10,20 C10,10 30,10 50,20 C70,30 90,10 90,20" stroke="white" stroke-width="2" fill="none">
                <animate attributeName="d" values="M10,20 C10,10 30,10 50,20 C70,30 90,10 90,20; M10,20 C10,30 30,30 50,20 C70,10 90,30 90,20; M10,20 C10,10 30,10 50,20 C70,30 90,10 90,20" dur="3s" repeatCount="indefinite" />
            </path>
        </svg>
    </section>
</body>
</html>