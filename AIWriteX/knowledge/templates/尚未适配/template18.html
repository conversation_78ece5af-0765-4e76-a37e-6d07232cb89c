<!DOCTYPE html>
<html lang="zh-CN">
<body style="margin:0; padding:0; font-family:'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif; line-height:1.6; color:#333; background-color:#f8f9fa;">

<section style="padding:5% 6%; background:linear-gradient(135deg, #FF9A8B 0%, #FF6A88 100%); color:white; border-radius:0 0 30px 30px; box-shadow:0 4px 15px rgba(255,106,136,0.3); margin-bottom:20px;">
  <div style="font-size:14px; letter-spacing:1px; margin-bottom:5px; font-weight:500; display:flex; align-items:center;">
    <svg width="18" height="18" viewBox="0 0 24 24" style="margin-right:6px;">
      <path fill="currentColor" d="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10-4.48 10-10S17.52,2 12,2zm1,15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
    </svg>
    豆瓣小组 | 上班族成长日记 · 最近
  </div>
  <h1 style="font-size:28px; font-weight:800; margin:15px 0 5px 0; line-height:1.3;">上班两年，我把自己养得真不错</h1>
</section>

<section style="padding:6% 7%; background-color:white; border-radius:16px; margin:0 5% 20px; box-shadow:0 4px 20px rgba(0,0,0,0.08);">
  <h2 style="font-size:20px; font-weight:700; margin:0 0 15px 0; padding-bottom:12px; border-bottom:2px solid #FF6A88; color:#333; letter-spacing:0.5px;">引言：照进现实的成人礼</h2>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">记得两年前刚踏入职场时，交完房租只能啃面包度日，PPT被批得像小学生作业，深夜加班回家看着体检单上的脂肪肝警告发呆。但最近整理相册突然发现，当初那个瑟瑟发抖的职场新人，居然活成了自己曾经羡慕的模样。</p>
</section>

<section style="padding:6% 7%; background-color:#f0f7ff; border-radius:16px; margin:0 5% 20px; box-shadow:0 4px 20px rgba(0,0,0,0.05);">
  <div style="display:flex; align-items:center; margin-bottom:15px;">
    <svg width="28" height="28" viewBox="0 0 24 24" style="margin-right:10px; color:#4285F4;">
      <path fill="currentColor" d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
    </svg>
    <h2 style="font-size:20px; font-weight:700; margin:0; color:#333; letter-spacing:0.5px;">一、钱包养成计划</h2>
  </div>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">月光族的觉醒</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">工作第三个月因为突发急性阑尾炎，发现存款连手术押金都不够。躺在急诊室接到家里转账时，像被浇了盆冰水。</p>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">记账APP救我命</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">强制设置每月咖啡基金300元封顶，意外发现手冲比星巴克便宜又好喝。最近打开存款账户看到五位数余额时，手指都在发抖。</p>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">第一次理财尝试</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">用三月存款买了支指数基金，虽然昨天看还浮亏5%，但再不会为价格标签焦虑的感觉太棒了！</p>
</section>

<section style="padding:6% 7%; background-color:white; border-radius:16px; margin:0 5% 20px; box-shadow:0 4px 20px rgba(0,0,0,0.08);">
  <div style="display:flex; align-items:center; margin-bottom:15px;">
    <svg width="28" height="28" viewBox="0 0 24 24" style="margin-right:10px; color:#FF6A88;">
      <path fill="currentColor" d="M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-6 0h-4V4h4v2z"/>
    </svg>
    <h2 style="font-size:20px; font-weight:700; margin:0; color:#333; letter-spacing:0.5px;">二、职场技能升级战</h2>
  </div>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">被PPT暴击的那天</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">第一次独立做汇报，被总监指着配色说"像西红柿炒鸡蛋"。下班躲进洗手间隔间哭了半小时，当晚就买了设计课。</p>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">深夜书房灯光</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">三个月啃完数据分析网课，现在处理Excel用函数比吃饭还熟练。上周做的市场分析报告直接成了部门模板。</p>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">提案通过时刻</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">上周站在会议室讲解自己设计的项目方案，看到老板点头时，突然发现手心里的汗不知什么时候干了。</p>
</section>

<section style="padding:6% 7%; background-color:#fdf6f0; border-radius:16px; margin:0 5% 20px; box-shadow:0 4px 20px rgba(0,0,0,0.05);">
  <div style="display:flex; align-items:center; margin-bottom:15px;">
    <svg width="28" height="28" viewBox="0 0 24 24" style="margin-right:10px; color:#FB8C00;">
      <path fill="currentColor" d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
    </svg>
    <h2 style="font-size:20px; font-weight:700; margin:0; color:#333; letter-spacing:0.5px;">三、生活重回正轨</h2>
  </div>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">告别外卖生活</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">连续吃坏两次肚子后，跟着B站学做快手菜。现在周末的番茄牛腩煲能香醒整个楼道。</p>

  <div style="display:flex; justify-content:center; margin:20px 0;">
    <img src="https://picsum.photos/400/220?random=1" style="width:100%; max-width:400px; border-radius:12px; box-shadow:0 4px 12px rgba(0,0,0,0.1);" alt="健康生活图片">
  </div>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">健身环大冒险</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">查出脂肪肝后买了健身环，最近体检指标全部变绿时，比拿年终奖还开心。</p>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">重启朋友圈</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">上个月生日，合租室友和同事突然端着蛋糕出现，才发现自己不再是角落里默默吃便当的独行侠。</p>
</section>

<section style="padding:6% 7%; background-color:white; border-radius:16px; margin:0 5% 20px; box-shadow:0 4px 20px rgba(0,0,0,0.08);">
  <div style="display:flex; align-items:center; margin-bottom:15px;">
    <svg width="28" height="28" viewBox="0 0 24 24" style="margin-right:10px; color:#4CAF50;">
      <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>
    <h2 style="font-size:20px; font-weight:700; margin:0; color:#333; letter-spacing:0.5px;">四、内心的重建工程</h2>
  </div>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">责任焦虑症治愈记</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">1. 第一次带新人交接项目，焦虑到三天没睡着。在师傅工位边偷师怎么清晰拆解任务，现在协调跨部门合作居然成了强项。</p>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">2. 周末在流浪动物中心当志愿者，照顾生病的奶猫学会耐心。经理说我最近做方案考虑问题周全多了。</p>

  <h3 style="font-size:17px; font-weight:600; margin:20px 0 10px 0; color:#333;">情绪急救包</h3>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; text-align:justify;">原来在手机备忘录写"夸夸清单"这么有用！项目受挫时就看看自己记录的27个小成就。</p>
  <p style="font-size:16px; line-height:1.8; margin:0 0 15px 0; font-weight:600; text-align:justify;">最近学会最好用的心理暗示：<span style="color:#FF6A88;">"两年前那个连打印机都不大会用的菜鸟，现在可是解决过数据危机的战士呢"</span></p>
</section>

<section style="padding:7% 8%; background:linear-gradient(135deg, #f0f7ff 0%, #e6f2ff 100%); border-radius:16px; margin:0 5% 30px; box-shadow:0 4px 20px rgba(0,0,0,0.08);">
  <h2 style="font-size:20px; font-weight:700; margin:0 0 15px 0; color:#333; letter-spacing:0.5px; text-align:center;">结语：把自己当项目经营</h2>
  <p style="font-size:16px; line-height:1.8; margin:0 0 20px 0; text-align:justify;">这两年最大的感悟是：成年人的体面都是自己挣的。卡里存款、肚子上消失的赘肉、PPT里越来越顺眼的图表，都在证明我在认真养育自己这个人生最重要项目。</p>
  <p style="font-size:16px; line-height:1.8; margin:0 0 20px 0; text-align:justify;">还在挣扎的姐妹们别急，就像我用来记账的手账本扉页写的那句：</p>
  
  <div style="background-color:white; padding:20px; border-radius:12px; margin:25px 0; box-shadow:0 4px 15px rgba(0,0,0,0.08);">
    <p style="font-size:18px; line-height:1.6; margin:0; text-align:center; font-weight:700; color:#FF6A88; font-style:italic;">
      "好日子不是某天突然降临的，是每天往存钱罐扔硬币的声音堆出来的"
    </p>
  </div>
  
  <svg width="100%" height="60" viewBox="0 0 400 60" style="margin-top:20px; overflow:visible;">
    <path d="M20,30 Q100,10 200,30 T380,30" stroke="#FF6A88" stroke-width="3" fill="none" stroke-linecap="round">
      <animate attributeName="d" 
               values="M20,30 Q100,10 200,30 T380,30;
                      M20,30 Q100,50 200,30 T380,30;
                      M20,30 Q100,10 200,30 T380,30" 
               dur="4s" 
               repeatCount="indefinite" />
    </path>
    <circle cx="20" cy="30" r="5" fill="#FF6A88">
      <animate attributeName="cy" values="30;25;30" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="200" cy="30" r="5" fill="#FF6A88">
      <animate attributeName="cy" values="30;35;30" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="380" cy="30" r="5" fill="#FF6A88">
      <animate attributeName="cy" values="30;25;30" dur="4s" repeatCount="indefinite" />
    </circle>
  </svg>
</section>

</body>
</html>