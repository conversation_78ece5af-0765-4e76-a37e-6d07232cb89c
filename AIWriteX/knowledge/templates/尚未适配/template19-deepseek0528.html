<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1"></head>
<body style="margin:0;padding:0;background-color:#f9f7f0;font-family:-apple-system,BlinkMacSystemFont,'Microsoft YaHei',sans-serif;line-height:1.6;color:#333;overflow-x:hidden;">
  
<!-- 头部卡片模块 -->
<section style="padding:24px 16px;background:linear-gradient(135deg, #1a2a6c, #b21f1f, #ffab1d);border-radius:0 0 32px 32px;box-shadow:0 12px 24px rgba(0,0,0,0.1);">
  <div style="padding:12px;background:rgba(255,255,255,0.15);border-radius:24px;border:1px solid rgba(255,255,255,0.2);backdrop-filter:blur(5px);">
    <h1 style="margin:0;font-size:1.8rem;font-weight:700;color:white;text-shadow:0 2px 4px rgba(0,0,0,0.2);">上班两年，我把自己养得真不错</h1>
    <div style="display:flex;align-items:center;margin-top:8px;">
      <div style="width:30px;height:30px;background:url('https://picsum.photos/100/100?random=1') center/cover;border-radius:50%;margin-right:8px;"></div>
      <div style="font-size:0.85rem;color:rgba(255,255,255,0.9);">豆瓣小组 | 上班族成长日记 · 最近</div>
    </div>
  </div>
</section>

<!-- 引言卡片模块 -->
<section style="margin:24px 16px;padding:24px;background-color:white;border-radius:24px;box-shadow:0 8px 24px rgba(178,31,31,0.12);">
  <h2 style="margin:0 0 16px;font-size:1.3rem;color:#b21f1f;font-weight:700;position:relative;padding-left:10px;">
    <span style="position:absolute;left:0;top:50%;transform:translateY(-50%);width:4px;height:24px;background:linear-gradient(to bottom, #ffab1d, #b21f1f);border-radius:2px;"></span>
    照进现实的成人礼
  </h2>
  <p style="font-size:1rem;color:#444;">记得两年前刚踏入职场时，交完房租只能啃面包度日，PPT被批得像小学生作业，深夜加班回家看着体检单上的脂肪肝警告发呆。但最近整理相册突然发现，当初那个瑟瑟发抖的职场新人，居然活成了自己曾经羡慕的模样。</p>
  <div style="display:flex;justify-content:space-around;margin-top:24px;">
    <svg viewBox="0 0 100 100" style="width:70px;height:70px;">
      <circle cx="50" cy="50" r="40" fill="#ffefd5" stroke="#ffab1d" stroke-width="2" />
      <line x1="15" y1="50" x2="85" y2="50" stroke="#b21f1f" stroke-width="4" stroke-linecap="round" />
      <line x1="50" y1="15" x2="50" y2="85" stroke="#b21f1f" stroke-width="4" stroke-linecap="round" />
      <text x="50" y="58" text-anchor="middle" fill="#1a2a6c" font-size="22" font-weight="bold">-$</text>
    </svg>
    <svg viewBox="0 0 100 100" style="width:70px;height:70px;">
      <rect x="25" y="20" width="50" height="60" rx="5" fill="#e8f4ff" stroke="#1a2a6c" stroke-width="2" />
      <rect x="35" y="30" width="30" height="15" fill="#1a2a6c50" rx="2" />
      <circle cx="50" cy="60" r="15" fill="#ffab1d" opacity="0.8" />
      <text x="50" y="65" text-anchor="middle" fill="white" font-size="14" font-weight="bold">PPT</text>
    </svg>
    <svg viewBox="0 0 100 100" style="width:70px;height:70px;">
      <circle cx="50" cy="50" r="40" fill="#f8f0e6" stroke="#ffab1d" stroke-width="2" />
      <line x1="30" y1="60" x2="70" y2="60" stroke="#b21f1f" stroke-width="4" stroke-linecap="round" />
      <line x1="40" y1="70" x2="60" y2="70" stroke="#b21f1f" stroke-width="3" stroke-linecap="round" />
      <text x="50" y="24" text-anchor="middle" fill="#1a2a6c" font-size="14" font-weight="bold">+4.3kg</text>
      <path d="M30,40 C40,30 60,30 70,40" stroke="#1a2a6c" stroke-width="2" fill="none"/>
    </svg>
  </div>
</section>

<!-- 钱包养成计划模块 -->
<section style="margin:24px 16px;padding:24px;background:linear-gradient(to bottom right, #e8f4ff, #ffffff);border-radius:24px;box-shadow:0 8px 24px rgba(26,42,108,0.1);">
  <div style="display:flex;align-items:center;margin-bottom:16px;">
    <svg viewBox="0 0 24 24" style="width:30px;height:30px;margin-right:10px;fill:#1a2a6c;">
      <path d="M5,6H23V20H5V6M14,9A3,3 0 0,1 17,12A3,3 0 0,1 14,15A3,3 0 0,1 11,12A3,3 0 0,1 14,9M9,8A2,2 0 0,1 7,10V14A2,2 0 0,1 9,16H19A2,2 0 0,1 21,14V10A2,2 0 0,1 19,8H9M5,2H19V4H5V2Z"/>
    </svg>
    <h2 style="margin:0;font-size:1.4rem;color:#1a2a6c;">钱包养成计划</h2>
  </div>
  
  <!-- 子卡片 -->
  <section style="margin:16px 0;padding:20px;background:white;border-radius:20px;box-shadow:0 4px 12px rgba(0,0,0,0.05);">
    <h3 style="margin:0 0 12px;font-size:1.1rem;color:#b21f1f;font-weight:600;">月光族的觉醒</h3>
    <p style="margin:0;font-size:0.95rem;color:#555;">工作第三个月因为突发急性阑尾炎，发现存款连手术押金都不够。躺在急诊室接到家里转账时，像被浇了盆冰水。</p>
    <div style="display:flex;align-items:center;margin-top:16px;">
      <svg viewBox="0 0 24 24" style="width:18px;height:18px;margin-right:5px;fill:#b21f1f;">
        <path d="M20 8H10C8.9 8 8 8.9 8 10V20C8 21.1 8.9 22 10 22H20C21.1 22 22 21.1 22 20V10C22 8.9 21.1 8 20 8M20 20H10V10H20V20M16 14H14V12H12V14H10V16H12V18H14V16H16V14M4 6H18V4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H8V18H4V6Z"/>
      </svg>
      <span style="font-size:0.9rem;color:#e53935;background-color:#ffebee;padding:4px 10px;border-radius:100px;">月光族→五位数存款</span>
    </div>
  </section>
  
  <section style="margin:16px 0;padding:20px;background:white;border-radius:20px;box-shadow:0 4px 12px rgba(0,0,0,0.05);">
    <h3 style="margin:0 0 12px;font-size:1.1rem;color:#b21f1f;font-weight:600;">记账APP救我命</h3>
    <p style="margin:0;font-size:0.95rem;color:#555;">强制设置每月咖啡基金300元封顶，意外发现手冲比星巴克便宜又好喝。最近打开存款账户看到五位数余额时，手指都在发抖。</p>
    <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:16px;">
      <div style="font-size:0.85rem;color:#1976d2;background-color:#e3f2fd;padding:4px 12px;border-radius:100px;">咖啡基金RMB 300</div>
      <div style="font-size:0.85rem;color:#388e3c;background-color:#e8f5e9;padding:4px 12px;border-radius:100px;">手冲咖啡福利</div>
      <div style="font-size:0.85rem;color:#f57c00;background-color:#fff3e0;padding:4px 12px;border-radius:100px;">五位数存款达成</div>
    </div>
  </section>
  
  <section style="margin:16px 0;padding:20px;background:white;border-radius:20px;box-shadow:0 4px 12px rgba(0,0,0,0.05);">
    <h3 style="margin:0 0 12px;font-size:1.1rem;color:#b21f1f;font-weight:600;">第一次理财尝试</h3>
    <p style="margin:0;font-size:0.95rem;color:#555;">用三月存款买了支指数基金，虽然昨天看还浮亏5%，但再不会为价格标签焦虑的感觉太棒了！</p>
    <div style="display:flex;align-items:center;justify-content:space-between;margin-top:16px;">
      <div style="display:flex;">
        <div style="width:60px;height:60px;">
          <svg viewBox="0 0 100 100" preserveAspectRatio="none">
            <polygon points="0,50 25,45 50,35 75,55 100,30 100,100 0,100" fill="rgba(26,115,233,0.1)" stroke="#1a73e8" stroke-width="1"/>
            <polyline points="0,50 25,45 50,35 75,55 100,30" fill="none" stroke="#1a73e8" stroke-width="3"/>
          </svg>
        </div>
        <div style="margin-left:16px;">
          <div style="font-size:0.8rem;color:#888;">指数基金投资</div>
          <div style="font-size:0.9rem;font-weight:600;color:#e53935;">浮动 -5%</div>
        </div>
      </div>
      <div style="background-color:#e8f5e9;border-radius:20px;padding:6px 16px;font-size:0.9rem;color:#388e3c;font-weight:600;">成长值 +97%</div>
    </div>
  </section>
</section>

<!-- 职场技能升级战模块 -->
<section style="margin:24px 16px;padding:24px;background:linear-gradient(to bottom left, #fff8e1, #ffffff);border-radius:24px;box-shadow:0 8px 24px rgba(255,171,29,0.12);">
  <div style="display:flex;align-items:center;margin-bottom:16px;">
    <svg viewBox="0 0 24 24" style="width:30px;height:30px;margin-right:10px;fill:#ffab1d;">
      <path d="M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
      <path d="M5,3C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3H5M5,5H19V19H5V5Z"/>
    </svg>
    <h2 style="margin:0;font-size:1.4rem;color:#ff6d00;">职场技能升级战</h2>
  </div>
  
  <!-- 子卡片 -->
  <section style="margin:16px 0;padding:20px;background:white;border-radius:20px;box-shadow:0 4px 12px rgba(255,171,29,0.1);position:relative;">
    <div style="position:absolute;top:20px;right:20px;font-size:0.8rem;padding:4px 8px;background:#ffecb3;border-radius:16px;color:#e65100;">
      ★ 突破时刻
    </div>
    <h3 style="margin:0 0 12px;font-size:1.1rem;color:#ff6d00;font-weight:600;">被PPT暴击的那天</h3>
    <p style="margin:0;font-size:0.95rem;color:#555;">第一次独立做汇报，被总监指着配色说"像西红柿炒鸡蛋"。下班躲进洗手间隔间哭了半小时，当晚就买了设计课。</p>
    <div style="display:grid;grid-template-columns:repeat(3,1fr);gap:12px;margin-top:16px;">
      <div style="display:flex;flex-direction:column;align-items:center;">
        <div style="width:60px;height:60px;border-radius:50%;background:#d32f2f;display:flex;align-items:center;justify-content:center;margin-bottom:8px;">
          <svg viewBox="0 0 24 24" style="width:30px;height:30px;fill:white;">
            <path d="M14.4,6H20V16H13L9,20V16H4V6H14.4M15,4H4A2,2 0 0,0 2,6V16A2,2 0 0,0 4,18H9V22L13,18H20A2,2 0 0,0 22,16V6A2,2 0 0,0 20,4H15Z"/>
          </svg>
        </div>
        <div style="font-size:0.75rem;color:#e53935;text-align:center;">报告被批</div>
      </div>
      <div style="display:flex;flex-direction:column;align-items:center;">
        <div style="width:60px;height:60px;border-radius:50%;background:#5e35b1;display:flex;align-items:center;justify-content:center;margin-bottom:8px;">
          <svg viewBox="0 0 24 24" style="width:30px;height:30px;fill:white;">
            <path d="M21,16.5C21,16.88 20.79,17.21 20.47,17.38L12.57,21.82C12.41,21.94 12.21,22 12,22C11.79,22 11.59,21.94 11.43,21.82L3.53,17.38C3.21,17.21 3,16.88 3,16.5V7.5C3,7.12 3.21,6.79 3.53,6.62L11.43,2.18C11.59,2.06 11.79,2 12,2C12.21,2 12.41,2.06 12.57,2.18L20.47,6.62C20.79,6.79 21,7.12 21,7.5V16.5M12,4.15L6.04,7.5L12,10.85L17.96,7.5L12,4.15Z"/>
          </svg>
        </div>
        <div style="font-size:0.75rem;color:#5e35b1;text-align:center;">设计学习</div>
      </div>
      <div style="display:flex;flex-direction:column;align-items:center;">
        <div style="width:60px;height:60px;border-radius:50%;background:#009688;display:flex;align-items:center;justify-content:center;margin-bottom:8px;">
          <svg viewBox="0 0 24 24" style="width:30px;height:30px;fill:white;">
            <path d="M14.06,9L15,9.94L5.92,19H5V18.08L14.06,9M17.66,3C17.41,3 17.15,3.1 16.96,3.29L15.13,5.12L18.88,8.87L20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18.17,3.09 17.92,3 17.66,3M14.06,6.19L3,17.25V21H6.75L17.81,9.94L14.06,6.19Z"/>
          </svg>
        </div>
        <div style="font-size:0.75rem;color:#009688;text-align:center;">部门模板</div>
      </div>
    </div>
  </section>
  
  <section style="margin:16px 0;padding:20px;background:white;border-radius:20px;box-shadow:0 4px 12px rgba(255,171,29,0.1);">
    <h3 style="margin:0 0 12px;font-size:1.1rem;color:#ff6d00;font-weight:600;">深夜书房灯光</h3>
    <p style="margin:0;font-size:0.95rem;color:#555;">三个月啃完数据分析网课，现在处理Excel用函数比吃饭还熟练。上周做的市场分析报告直接成了部门模板。</p>
    <div style="display:flex;align-items:center;justify-content:center;margin-top:16px;gap:8px;flex-wrap:wrap;">
      <span style="font-size:0.8rem;padding:6px 12px;background:#ffee58;color:#f57f17;border-radius:100px;">VLOOKUP</span>
      <span style="font-size:0.8rem;padding:6px 12px;background:#80deea;color:#00838f;border-radius:100px;">Pivot Table</span>
      <span style="font-size:0.8rem;padding:6px 12px;background:#ce93d8;color:#6a1b9a;border-radius:100px;">Power Query</span>
    </div>
  </section>
  
  <section style="margin:16px 0;padding:20px;background:white;border-radius:20px;box-shadow:0 4px 12px rgba(255,171,29,0.1);">
    <h3 style="margin:0 0 12px;font-size:1.1rem;color:#ff6d00;font-weight:600;">提案通过时刻</h3>
    <p style="margin:0;font-size:0.95rem;color:#555;">上周站在会议室讲解自己设计的项目方案，看到老板点头时，突然发现手心里的汗不知什么时候干了。</p>
    <div style="margin-top:16px;background:#fffde7;border-radius:12px;padding:16px;position:relative;border-left:4px solid #ffd600;">
      <svg viewBox="0 0 24 24" style="width:40px;height:40px;position:absolute;right:10px;top:-20px;fill:#ffd600;background:#fff8e1;border-radius:50%;">
        <path d="M21.4,11.6L12.4,2.6C12,2.2 11.5,2 11,2C10.5,2 10,2.2 9.6,2.6L2.6,9.6C1.8,10.4 1.8,11.6 2.6,12.4L9.9,19.7C10.3,20.1 10.9,20.1 11.3,19.7L21.4,9.6C22.2,8.8 22.2,7.4 21.4,6.6V11.6Z"/>
      </svg>
      <div style="font-size:0.9rem;color:#827717;font-weight:600;">🎯 技术成长值</div>
      <div style="height:8px;background:#e8eaf6;border-radius:4px;margin:10px 0;overflow:hidden;">
        <div style="height:100%;width:72%;background:linear-gradient(90deg, #ffab1d, #ffd600);border-radius:4px;"></div>
      </div>
      <div style="display:flex;justify-content:space-between;font-size:0.8rem;color:#616161;">
        <span>演讲恐惧</span>
        <span>项目主导者</span>
      </div>
    </div>
  </section>
</section>

<!-- 生活模块和结语模块(为节省篇幅，简要展示) -->
<section style="margin:24px 16px;padding:24px;background:linear-gradient(145deg, #fff3e0, #ffffff);border-radius:24px;box-shadow:0 8px 24px rgba(229,57,53,0.1);">
  <div style="display:flex;align-items:center;margin-bottom:16px;">
    <svg viewBox="0 0 24 24" style="width:30px;height:30px;margin-right:10px;fill:#e53935;">
      <path d="M6.5,6C7.47,6 8.37,6.5 9.11,7.38C9.66,8.07 10,9 10,10C10,12.8 7.87,14.14 5.84,15.3C5.34,15.59 4.84,15.87 4.35,16.14C3.83,16.41 3.31,16.65 2.79,16.88L2.79,17.15C4.91,17.9 7.24,18 9,18C13,18 14,16 14,16C14,16 17,18 21,18C22,18 22,17 22,16.5V11.5A3.5,3.5 0 0,0 18.5,8C18.5,8 15,8 13.5,6.5C13.5,6.5 13.44,6.17 13.1,5.83C12.28,5.05 11.5,5 11.5,5H10.5A2.5,2.5 0 0,0 8,7.5A2.5,2.5 0 0,0 10.5,10C11.82,10 12.72,8.84 12.84,8.12C12.92,8.63 13,9.11 13,9.5C13,11.4 12,12 10.5,12C9.25,12 8.6,11.78 7.41,10.97C5.5,11.65 3,12.38 3,9.5C3,7.6 4.6,6 6.5,6M11.5,5A1.5,1.5 0 0,1 13,6.5A1.5,1.5 0 0,1 11.5,8A1.5,1.5 0 0,1 10,6.5A1.5,1.5 0 0,1 11.5,5Z"/>
    </svg>
    <h2 style="margin:0;font-size:1.4rem;color:#e53935;">生活重回正轨</h2>
  </div>
  <!-- 内部卡片内容 -->
  <section style="margin:16px 0;padding:20px;background:white;border-radius:20px;box-shadow:0 4px 12px rgba(229,57,53,0.1);">
    <h3 style="margin:0 0 12px;font-size:1.1rem;color:#e53935;font-weight:600;">告别外卖生活</h3>
    <p style="margin:0;font-size:0.95rem;color:#555;">连续吃坏两次肚子后，跟着B站学做快手菜。现在周末的番茄牛腩煲能香醒整个楼道。</p>
  </section>
</section>

<!-- 内心重建模块 -->
<section style="margin:24px 16px;padding:24px;background:linear-gradient(145deg, #f3e5f5, #ffffff);border-radius:24px;box-shadow:0 8px 24px rgba(74,20,140,0.1);">
  <div style="display:flex;align-items:center;margin-bottom:16px;">
    <svg viewBox="0 0 24 24" style="width:30px;height:30px;margin-right:10px;fill:#6a1b9a;">
      <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
    </svg>
    <h2 style="margin:0;font-size:1.4rem;color:#6a1b9a;">内心的重建工程</h2>
  </div>
  <!-- 内部卡片内容 -->
</section>

<!-- 结束语卡片 -->
<section style="margin:24px 16px;padding:24px;background:linear-gradient(135deg, #1a2a6c, #4a148c);border-radius:24px;box-shadow:0 12px 24px rgba(0,0,0,0.25);position:relative;overflow:hidden;">
  <div style="position:absolute;top:-50px;right:-50px;width:150px;height:150px;border-radius:50%;background:rgba(255,255,255,0.15);"></div>
  <div style="position:relative;">
    <p style="font-size:1.1rem;color:white;margin:0 0 24px;">这两年最大的感悟是：成年人的体面都是自己挣的。卡里存款、肚子上消失的赘肉、PPT里越来越顺眼的图表，都在证明我在认真养育自己这个人生最重要项目。</p>
    <div style="padding:16px;background:rgba(255,255,255,0.9);border-radius:16px;">
      <div style="display:flex;align-items:flex-start;margin-bottom:12px;">
        <svg viewBox="0 0 24 24" style="width:24px;height:24px;margin-right:10px;fill:#ffab1d;">
          <path d="M12,17L18,21V13H24L12,1L0,13H6V21L12,17Z"/>
        </svg>
        <div>
          <p style="margin:0;font-size:1.1rem;font-weight:600;color:#1a237e;line-height:1.4;">"好日子不是某天突然降临的，是每天往存钱罐扔硬币的声音堆出来的"</p>
        </div>
      </div>
      <p style="margin:0;font-size:0.9rem;color:#5e35b1;">还在挣扎的姐妹们别急，就像我用来记账的手账本扉页写的那句</p>
    </div>
  </div>
  <!-- 装饰圆环 -->
  <div style="position:absolute;bottom:-30px;left:-30px;width:100px;height:100px;border-radius:50%;border:4px solid rgba(255,255,255,0.15);"></div>
</section>

</body>
</html>
