#!/usr/bin/env python3
"""
测试OpenRouter不同免费模型的可用性
"""

import requests
import json
import time

def test_openrouter_models():
    """测试OpenRouter不同免费模型"""
    print("🔑 测试 OpenRouter 不同免费模型...")
    
    # 您的所有OpenRouter Keys
    openrouter_keys = [
        "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",
        "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",
        "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",
        "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",
        "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"
    ]
    
    # 常见的免费模型
    free_models = [
        "meta-llama/llama-3.2-3b-instruct:free",
        "microsoft/phi-3-mini-128k-instruct:free", 
        "qwen/qwen-2-7b-instruct:free",
        "google/gemma-2-9b-it:free",
        "huggingfaceh4/zephyr-7b-beta:free",
        "openrouter/auto"  # 自动选择免费模型
    ]
    
    results = {}
    
    # 测试每个模型
    for model in free_models:
        print(f"\n🤖 测试模型: {model}")
        results[model] = {"valid_keys": 0, "working_keys": []}
        
        # 测试前5个Keys
        for i, key in enumerate(openrouter_keys):
            try:
                response = requests.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "https://aiwritex.com",
                        "X-Title": "AIWriteX Model Test"
                    },
                    json={
                        "model": model,
                        "messages": [{"role": "user", "content": "Hello"}],
                        "max_tokens": 20
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'choices' in data and len(data['choices']) > 0:
                        results[model]["valid_keys"] += 1
                        results[model]["working_keys"].append(f"Key{i+1}")
                        print(f"   ✅ Key {i+1} 可用")
                    else:
                        print(f"   ❌ Key {i+1} 响应格式异常")
                elif response.status_code == 402:
                    print(f"   💰 Key {i+1} 余额不足")
                elif response.status_code == 429:
                    print(f"   ⏰ Key {i+1} 请求过频")
                else:
                    print(f"   ❌ Key {i+1} 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ Key {i+1} 错误: {str(e)[:50]}")
            
            time.sleep(1)  # 避免请求过快
    
    return results

def main():
    """主函数"""
    print("🚀 OpenRouter 免费模型测试工具")
    print("=" * 60)
    
    # 测试不同模型
    results = test_openrouter_models()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    best_model = None
    max_valid_keys = 0
    
    for model, result in results.items():
        valid_count = result["valid_keys"]
        print(f"\n🤖 {model}")
        print(f"   ✅ 可用Keys: {valid_count}/5")
        print(f"   🔑 工作的Keys: {', '.join(result['working_keys'])}")
        
        if valid_count > max_valid_keys:
            max_valid_keys = valid_count
            best_model = model
    
    print(f"\n🏆 推荐配置:")
    if best_model:
        print(f"   最佳模型: {best_model}")
        print(f"   可用Keys: {max_valid_keys}/5")
        
        print(f"\n🔧 AIWriteX 配置建议:")
        print(f"   api_type: OpenRouter")
        print(f"   model: {best_model}")
        print(f"   预估总可用Keys: {max_valid_keys * 4} (基于19个Keys)")
        
        # 更新配置建议
        if max_valid_keys >= 3:
            print(f"\n✅ 配置状态: 良好")
            print(f"   - 足够的可用Keys支持正常使用")
            print(f"   - 建议使用模型: {best_model}")
        else:
            print(f"\n⚠️  配置状态: 需要优化")
            print(f"   - 可用Keys较少，可能影响使用体验")
            print(f"   - 建议尝试其他免费模型")
    else:
        print("❌ 未找到可用的模型配置")

if __name__ == "__main__":
    main()
