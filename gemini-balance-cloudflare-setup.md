# Gemini-Balance Cloudflare 部署指南

## 1. 准备工作

### 获取Gemini API Keys
```bash
# 批量注册Google账号
# 每个账号访问: https://aistudio.google.com/app/apikey
# 创建API Key并记录

# 示例Keys列表：
API_KEYS = [
    "AIzaSyA1234567890abcdefghijklmnop1",
    "AIzaSyB1234567890abcdefghijklmnop2", 
    "AIzaSyC1234567890abcdefghijklmnop3",
    # ... 更多Keys
]
```

### 设置访问令牌
```bash
ALLOWED_TOKENS = [
    "sk-your-custom-token-1",
    "sk-your-custom-token-2",
    "sk-your-custom-token-3"
]
```

## 2. Docker部署方式

### 方式1：Docker Compose（推荐）
```yaml
# docker-compose.yml
version: '3.8'
services:
  gemini-balance:
    image: ghcr.io/snailyp/gemini-balance:latest
    ports:
      - "8000:8000"
    environment:
      - DATABASE_TYPE=sqlite
      - SQLITE_DATABASE=/app/data/gemini_balance.db
      - API_KEYS=["AIzaSyA123...", "AIzaSyB123...", "AIzaSyC123..."]
      - ALLOWED_TOKENS=["sk-your-token-1", "sk-your-token-2"]
      - AUTH_TOKEN=sk-your-admin-token
      - MAX_FAILURES=3
      - MAX_RETRIES=3
      - CHECK_INTERVAL_HOURS=1
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
    restart: unless-stopped
```

### 方式2：直接Docker运行
```bash
# 创建数据目录
mkdir -p ./data

# 运行容器
docker run -d \
  --name gemini-balance \
  -p 8000:8000 \
  -v ./data:/app/data \
  -e DATABASE_TYPE=sqlite \
  -e SQLITE_DATABASE=/app/data/gemini_balance.db \
  -e 'API_KEYS=["AIzaSyA123...", "AIzaSyB123..."]' \
  -e 'ALLOWED_TOKENS=["sk-your-token"]' \
  -e AUTH_TOKEN=sk-your-admin-token \
  ghcr.io/snailyp/gemini-balance:latest
```

## 3. Cloudflare Tunnel部署

### 安装Cloudflare Tunnel
```bash
# 下载cloudflared
curl -L --output cloudflared.deb https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared.deb

# 登录Cloudflare
cloudflared tunnel login

# 创建隧道
cloudflared tunnel create gemini-balance

# 配置DNS
cloudflared tunnel route dns gemini-balance api.yourdomain.com
```

### 配置文件
```yaml
# ~/.cloudflared/config.yml
tunnel: gemini-balance
credentials-file: ~/.cloudflared/your-tunnel-id.json

ingress:
  - hostname: api.yourdomain.com
    service: http://localhost:8000
  - service: http_status:404
```

### 启动服务
```bash
# 启动Gemini-Balance
docker-compose up -d

# 启动Cloudflare Tunnel
cloudflared tunnel run gemini-balance
```

## 4. 配置优化

### 环境变量配置
```bash
# .env 文件
DATABASE_TYPE=sqlite
SQLITE_DATABASE=/app/data/gemini_balance.db

# API配置
API_KEYS=["key1", "key2", "key3", ...]
ALLOWED_TOKENS=["sk-token1", "sk-token2"]
AUTH_TOKEN=sk-admin-token

# 性能优化
MAX_FAILURES=3
MAX_RETRIES=3
CHECK_INTERVAL_HOURS=1
TIME_OUT=300

# 模型配置
TEST_MODEL=gemini-1.5-flash
IMAGE_MODELS=["gemini-2.0-flash-exp"]
SEARCH_MODELS=["gemini-2.0-flash-exp"]

# 日志配置
LOG_LEVEL=INFO
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7
```

## 5. 监控和管理

### Web管理界面
```bash
# 访问管理界面
https://api.yourdomain.com/keys_status

# 使用AUTH_TOKEN登录
# 可以查看：
# - Key状态和使用情况
# - 请求日志和错误日志  
# - 实时监控和统计
```

### API端点
```bash
# OpenAI兼容格式
Base URL: https://api.yourdomain.com/v1
Authorization: Bearer sk-your-token

# Gemini原生格式  
Base URL: https://api.yourdomain.com/gemini/v1beta
Authorization: Bearer sk-your-token

# 模型列表
GET https://api.yourdomain.com/v1/models
```

## 6. 扩展功能

### 图像生成
```bash
# 配置图像上传
UPLOAD_PROVIDER=smms
SMMS_SECRET_TOKEN=your-smms-token

# 或使用Cloudflare ImgBed
UPLOAD_PROVIDER=cloudflare_imgbed
CLOUDFLARE_IMGBED_URL=https://your-imgbed.pages.dev/upload
CLOUDFLARE_IMGBED_AUTH_CODE=your-auth-code
```

### 代理支持
```bash
# 如果需要代理访问Google API
PROXIES=["http://proxy1:port", "socks5://proxy2:port"]
```

## 7. 高可用部署

### 多实例负载均衡
```yaml
# docker-compose-ha.yml
version: '3.8'
services:
  gemini-balance-1:
    image: ghcr.io/snailyp/gemini-balance:latest
    ports:
      - "8001:8000"
    env_file: .env
    
  gemini-balance-2:
    image: ghcr.io/snailyp/gemini-balance:latest  
    ports:
      - "8002:8000"
    env_file: .env
    
  nginx:
    image: nginx:alpine
    ports:
      - "8000:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### Nginx负载均衡配置
```nginx
# nginx.conf
upstream gemini_backend {
    server gemini-balance-1:8000;
    server gemini-balance-2:8000;
}

server {
    listen 80;
    location / {
        proxy_pass http://gemini_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```
