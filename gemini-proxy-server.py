#!/usr/bin/env python3
"""
本地Gemini代理服务器
解决Gemini API国内访问问题
"""

from flask import Flask, request, jsonify, Response
import requests
import json
import time
import random
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# 您的Gemini API Keys
GEMINI_KEYS = [
    "AIzaSyDXLZJ8o_kVRw5qSOXwDYcS_qjYS-A-WHQ",
    "AIzaSyBIE2r04LGUwbXD-lzup9zRtY3Db8pjBJ8"
]

# 允许的访问令牌
ALLOWED_TOKENS = [
    "sk-gemini-proxy-1",
    "sk-gemini-proxy-2", 
    "sk-gemini-proxy-admin"
]

# Key使用统计
key_usage_stats = {}

def get_next_api_key():
    """获取下一个可用的API Key"""
    now = time.time()
    best_key = GEMINI_KEYS[0]
    min_usage = float('inf')
    
    for key in GEMINI_KEYS:
        usage = key_usage_stats.get(key, {'count': 0, 'last_used': 0})
        
        # 如果超过1小时没使用，重置计数
        if now - usage['last_used'] > 3600:
            usage['count'] = 0
        
        if usage['count'] < min_usage:
            min_usage = usage['count']
            best_key = key
    
    # 更新使用统计
    if best_key not in key_usage_stats:
        key_usage_stats[best_key] = {'count': 0, 'last_used': 0}
    
    key_usage_stats[best_key]['count'] += 1
    key_usage_stats[best_key]['last_used'] = now
    
    return best_key

def validate_token(auth_header):
    """验证访问令牌"""
    if not auth_header or not auth_header.startswith('Bearer '):
        return False
    
    token = auth_header[7:]
    return token in ALLOWED_TOKENS

def convert_openai_to_gemini(openai_request):
    """转换OpenAI格式到Gemini格式"""
    messages = openai_request.get('messages', [])
    model = openai_request.get('model', 'gemini-1.5-flash')
    max_tokens = openai_request.get('max_tokens', 1000)
    temperature = openai_request.get('temperature', 0.7)
    
    # 转换消息格式
    contents = []
    for message in messages:
        if message['role'] == 'user':
            contents.append({
                'parts': [{'text': message['content']}]
            })
        elif message['role'] == 'assistant':
            contents.append({
                'role': 'model',
                'parts': [{'text': message['content']}]
            })
    
    gemini_request = {
        'contents': contents,
        'generationConfig': {
            'maxOutputTokens': max_tokens,
            'temperature': temperature
        }
    }
    
    return gemini_request, model.replace('gemini-', 'gemini-')

def convert_gemini_to_openai(gemini_response, model):
    """转换Gemini响应到OpenAI格式"""
    if not gemini_response.get('candidates'):
        raise Exception('No candidates in Gemini response')
    
    candidate = gemini_response['candidates'][0]
    content = candidate['content']['parts'][0]['text']
    
    return {
        'id': f'chatcmpl-{int(time.time())}',
        'object': 'chat.completion',
        'created': int(time.time()),
        'model': model,
        'choices': [{
            'index': 0,
            'message': {
                'role': 'assistant',
                'content': content
            },
            'finish_reason': 'stop'
        }],
        'usage': {
            'prompt_tokens': 0,
            'completion_tokens': len(content.split()),
            'total_tokens': len(content.split())
        }
    }

@app.route('/v1/models', methods=['GET'])
def list_models():
    """返回支持的模型列表"""
    if not validate_token(request.headers.get('Authorization')):
        return jsonify({'error': 'Unauthorized'}), 401
    
    models = {
        'object': 'list',
        'data': [
            {'id': 'gemini-1.5-flash', 'object': 'model', 'created': 1677610602, 'owned_by': 'google'},
            {'id': 'gemini-1.5-pro', 'object': 'model', 'created': 1677610602, 'owned_by': 'google'},
            {'id': 'gemini-2.0-flash', 'object': 'model', 'created': 1677610602, 'owned_by': 'google'}
        ]
    }
    
    return jsonify(models)

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """处理聊天完成请求"""
    if not validate_token(request.headers.get('Authorization')):
        return jsonify({'error': 'Unauthorized'}), 401
    
    try:
        openai_request = request.json
        gemini_request, model = convert_openai_to_gemini(openai_request)
        
        # 获取API Key
        api_key = get_next_api_key()
        
        # 调用Gemini API
        gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"
        
        response = requests.post(
            gemini_url,
            headers={'Content-Type': 'application/json'},
            json=gemini_request,
            timeout=30
        )
        
        if response.status_code == 200:
            gemini_data = response.json()
            openai_response = convert_gemini_to_openai(gemini_data, model)
            return jsonify(openai_response)
        else:
            return jsonify({
                'error': f'Gemini API error: {response.status_code}',
                'details': response.text
            }), response.status_code
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/status', methods=['GET'])
def status():
    """返回服务状态"""
    return jsonify({
        'status': 'running',
        'service': 'Gemini Proxy Server',
        'api_keys_count': len(GEMINI_KEYS),
        'key_usage_stats': key_usage_stats,
        'supported_models': ['gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-2.0-flash']
    })

@app.route('/', methods=['GET'])
def home():
    """首页"""
    return """
    <h1>🚀 Gemini Proxy Server</h1>
    <p>本地Gemini API代理服务</p>
    <ul>
        <li><strong>API端点:</strong> http://localhost:8080/v1</li>
        <li><strong>状态页面:</strong> <a href="/status">http://localhost:8080/status</a></li>
        <li><strong>支持模型:</strong> gemini-1.5-flash, gemini-1.5-pro, gemini-2.0-flash</li>
    </ul>
    <h3>访问令牌:</h3>
    <ul>
        <li>sk-gemini-proxy-1</li>
        <li>sk-gemini-proxy-2</li>
        <li>sk-gemini-proxy-admin</li>
    </ul>
    """

if __name__ == '__main__':
    print("🚀 启动Gemini代理服务器...")
    print("📡 访问地址: http://localhost:8080")
    print("🔑 访问令牌: sk-gemini-proxy-1, sk-gemini-proxy-2, sk-gemini-proxy-admin")
    print("📊 状态页面: http://localhost:8080/status")
    
    app.run(host='0.0.0.0', port=8080, debug=False)
