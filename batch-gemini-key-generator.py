#!/usr/bin/env python3
"""
批量Gemini API Key管理工具
用于管理多个Google账号和API Keys
"""

import json
import requests
import time
from typing import List, Dict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiKeyManager:
    def __init__(self):
        self.accounts = []
        self.valid_keys = []
        self.invalid_keys = []
        
    def load_accounts(self, accounts_file: str):
        """从文件加载账号信息"""
        try:
            with open(accounts_file, 'r', encoding='utf-8') as f:
                self.accounts = json.load(f)
            logger.info(f"加载了 {len(self.accounts)} 个账号")
        except FileNotFoundError:
            logger.error(f"账号文件 {accounts_file} 不存在")
            
    def test_api_key(self, api_key: str) -> bool:
        """测试API Key是否有效"""
        url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        headers = {"Content-Type": "application/json"}
        data = {
            "contents": [{"parts": [{"text": "Hello"}]}]
        }
        
        try:
            response = requests.post(
                f"{url}?key={api_key}",
                headers=headers,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"API Key 有效: {api_key[:20]}...")
                return True
            else:
                logger.warning(f"API Key 无效: {api_key[:20]}... - {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"测试API Key失败: {e}")
            return False
    
    def batch_test_keys(self, api_keys: List[str]):
        """批量测试API Keys"""
        logger.info(f"开始测试 {len(api_keys)} 个API Keys")
        
        for i, key in enumerate(api_keys):
            logger.info(f"测试进度: {i+1}/{len(api_keys)}")
            
            if self.test_api_key(key):
                self.valid_keys.append(key)
            else:
                self.invalid_keys.append(key)
                
            # 避免请求过快
            time.sleep(1)
            
        logger.info(f"测试完成: 有效 {len(self.valid_keys)} 个, 无效 {len(self.invalid_keys)} 个")
    
    def get_key_quota(self, api_key: str) -> Dict:
        """获取API Key的配额信息"""
        # 注意：Gemini API目前没有直接的配额查询接口
        # 这里返回默认的免费配额信息
        return {
            "model": "gemini-1.5-flash",
            "requests_per_minute": 15,
            "requests_per_day": 1500,
            "tokens_per_minute": 1000000,
            "status": "active"
        }
    
    def generate_config(self, output_file: str = "gemini_keys_config.json"):
        """生成配置文件"""
        config = {
            "api_keys": self.valid_keys,
            "total_keys": len(self.valid_keys),
            "estimated_daily_requests": len(self.valid_keys) * 1500,
            "estimated_hourly_requests": len(self.valid_keys) * 62,
            "key_details": []
        }
        
        for i, key in enumerate(self.valid_keys):
            config["key_details"].append({
                "key_id": f"key_{i+1}",
                "api_key": key,
                "quota": self.get_key_quota(key),
                "status": "active"
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        logger.info(f"配置文件已生成: {output_file}")
        return config
    
    def generate_gemini_balance_config(self):
        """生成gemini-balance项目的配置"""
        if not self.valid_keys:
            logger.error("没有有效的API Keys")
            return
            
        # 生成.env配置
        env_config = f"""# Gemini Balance 配置文件
DATABASE_TYPE=sqlite
SQLITE_DATABASE=/app/data/gemini_balance.db

# API Keys (共{len(self.valid_keys)}个)
API_KEYS={json.dumps(self.valid_keys)}

# 访问令牌
ALLOWED_TOKENS=["sk-your-token-1", "sk-your-token-2", "sk-your-token-3"]
AUTH_TOKEN=sk-admin-token

# 性能配置
MAX_FAILURES=3
MAX_RETRIES=3
CHECK_INTERVAL_HOURS=1
TIME_OUT=300

# 模型配置
TEST_MODEL=gemini-1.5-flash
IMAGE_MODELS=["gemini-2.0-flash-exp"]
SEARCH_MODELS=["gemini-2.0-flash-exp"]

# 日志配置
LOG_LEVEL=INFO
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7

# 预估性能
# 总Keys数量: {len(self.valid_keys)}
# 预估每日请求: {len(self.valid_keys) * 1500}
# 预估每小时请求: {len(self.valid_keys) * 62}
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_config)
            
        logger.info("Gemini-Balance .env 配置文件已生成")
        
        # 生成docker-compose.yml
        docker_config = f"""version: '3.8'
services:
  gemini-balance:
    image: ghcr.io/snailyp/gemini-balance:latest
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    
  # 可选：添加nginx反向代理
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #   depends_on:
  #     - gemini-balance
"""
        
        with open('docker-compose.yml', 'w', encoding='utf-8') as f:
            f.write(docker_config)
            
        logger.info("Docker Compose 配置文件已生成")

def main():
    """主函数"""
    manager = GeminiKeyManager()
    
    # 示例：手动添加API Keys进行测试
    api_keys = [
        "AIzaSyA1234567890abcdefghijklmnop1",  # 替换为实际的API Key
        "AIzaSyB1234567890abcdefghijklmnop2",  # 替换为实际的API Key
        "AIzaSyC1234567890abcdefghijklmnop3",  # 替换为实际的API Key
        # 添加更多API Keys...
    ]
    
    print("=== Gemini API Key 批量管理工具 ===")
    print(f"准备测试 {len(api_keys)} 个API Keys")
    
    # 批量测试
    manager.batch_test_keys(api_keys)
    
    # 生成配置
    if manager.valid_keys:
        config = manager.generate_config()
        manager.generate_gemini_balance_config()
        
        print(f"\n=== 测试结果 ===")
        print(f"有效Keys: {len(manager.valid_keys)}")
        print(f"无效Keys: {len(manager.invalid_keys)}")
        print(f"预估每日请求量: {len(manager.valid_keys) * 1500}")
        print(f"预估每小时请求量: {len(manager.valid_keys) * 62}")
        
        print(f"\n=== 配置文件 ===")
        print("✅ gemini_keys_config.json - 详细配置")
        print("✅ .env - Gemini-Balance环境变量")
        print("✅ docker-compose.yml - Docker部署配置")
        
    else:
        print("❌ 没有找到有效的API Keys")

if __name__ == "__main__":
    main()
